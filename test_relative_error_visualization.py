#!/usr/bin/env python3
"""
测试相对百分比误差可视化功能
演示如何使用新的相对百分比误差图表功能
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.universal_visualizer import UniversalVisualizer
from src.utils.config import setup_matplotlib, get_current_paths

def generate_sample_data():
    """生成示例数据来测试可视化功能"""
    
    # 生成时间序列数据
    n_samples = 500
    time_steps = np.arange(n_samples)
    
    # 生成真实值（带有一些趋势和噪声）
    trend = 0.1 * time_steps + 50
    seasonal = 10 * np.sin(2 * np.pi * time_steps / 50)
    noise = np.random.normal(0, 2, n_samples)
    y_true = trend + seasonal + noise
    
    # 确保没有零值（避免除零错误）
    y_true = np.where(y_true <= 0, 1, y_true)
    
    # 生成不同模型的预测值
    models_predictions = {}
    
    # 模型1: CNN-BI-LSTM-AM (较好的性能)
    noise1 = np.random.normal(0, 1.5, n_samples)
    bias1 = 0.02 * y_true  # 2% 系统性偏差
    models_predictions['CNN-BI-LSTM-AM'] = y_true + noise1 + bias1
    
    # 模型2: CNN-LSTM-AM (中等性能)
    noise2 = np.random.normal(0, 2.5, n_samples)
    bias2 = -0.03 * y_true  # -3% 系统性偏差
    models_predictions['CNN-LSTM-AM'] = y_true + noise2 + bias2
    
    # 模型3: CNN-BI-LSTM (较差的性能)
    noise3 = np.random.normal(0, 3.0, n_samples)
    bias3 = 0.05 * y_true  # 5% 系统性偏差
    models_predictions['CNN-BI-LSTM'] = y_true + noise3 + bias3
    
    # 模型4: CNN-LSTM (最差的性能)
    noise4 = np.random.normal(0, 4.0, n_samples)
    bias4 = -0.07 * y_true  # -7% 系统性偏差
    models_predictions['CNN-LSTM'] = y_true + noise4 + bias4
    
    # 模型5: CNN-RNN (中等偏下性能)
    noise5 = np.random.normal(0, 3.5, n_samples)
    bias5 = 0.04 * y_true  # 4% 系统性偏差
    models_predictions['CNN-RNN'] = y_true + noise5 + bias5
    
    # 模型6: CNN-GRU (中等偏上性能)
    noise6 = np.random.normal(0, 2.0, n_samples)
    bias6 = -0.01 * y_true  # -1% 系统性偏差
    models_predictions['CNN-GRU'] = y_true + noise6 + bias6
    
    return y_true, models_predictions

def create_results_format(y_true, models_predictions):
    """将数据转换为UniversalVisualizer期望的格式"""
    results = {}
    
    for model_name, y_pred in models_predictions.items():
        results[model_name] = {
            'test': {
                'MAE': np.mean(np.abs(y_pred - y_true)),
                'MSE': np.mean((y_pred - y_true) ** 2),
                'R2': 1 - np.sum((y_pred - y_true) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)
            },
            'predictions': {
                'test': (y_true, y_pred)
            }
        }
    
    return results

def main():
    """主函数"""
    print("🎨 测试相对百分比误差可视化功能")
    print("=" * 60)
    
    # 设置matplotlib
    setup_matplotlib()
    
    # 生成示例数据
    print("📊 生成示例数据...")
    y_true, models_predictions = generate_sample_data()
    
    # 转换为所需格式
    results = create_results_format(y_true, models_predictions)
    
    print(f"✅ 生成了 {len(models_predictions)} 个模型的预测数据")
    print(f"📈 数据点数量: {len(y_true)}")
    
    # 创建可视化器
    visualizer = UniversalVisualizer()
    
    # 创建保存目录
    save_dir = os.path.join(get_current_paths()['figures'], 'test_relative_error')
    os.makedirs(save_dir, exist_ok=True)
    
    # 生成相对百分比误差图
    print("\n🎯 生成相对百分比误差图（带插图）...")
    
    try:
        filepath = visualizer._plot_relative_percentage_error_with_inset(
            results=results,
            dataset='test',
            save_dir=save_dir,
            experiment_name='CNN_Models_Comparison'
        )
        
        if filepath:
            print(f"✅ 图表已保存到: {filepath}")
            print(f"📁 完整路径: {os.path.abspath(filepath)}")
        else:
            print("❌ 图表生成失败")
            
    except Exception as e:
        print(f"❌ 生成图表时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 测试完成！")
    print("💡 提示: 你可以在生成的图表中看到:")
    print("   - 多个模型的相对百分比误差对比")
    print("   - 不同的线型和颜色")
    print("   - 右上角的插图显示局部细节")
    print("   - 学术风格的图表样式")

if __name__ == "__main__":
    main()
