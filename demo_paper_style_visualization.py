#!/usr/bin/env python3
"""
演示论文风格的相对百分比误差可视化
模仿你提供的论文图表样式
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.universal_visualizer import UniversalVisualizer
from src.utils.config import setup_matplotlib, get_current_paths

def create_paper_style_demo():
    """创建论文风格的演示数据和可视化"""
    
    print("🎨 生成论文风格的相对百分比误差图")
    print("=" * 60)
    
    # 设置matplotlib
    setup_matplotlib()
    
    # 生成更真实的时间序列数据
    n_samples = 400
    time_steps = np.arange(n_samples)
    
    # 生成基础信号（模拟风电功率）
    # 包含趋势、季节性和随机波动
    trend = 0.05 * time_steps
    daily_cycle = 15 * np.sin(2 * np.pi * time_steps / 24)  # 日周期
    weekly_cycle = 8 * np.sin(2 * np.pi * time_steps / 168)  # 周周期
    random_walk = np.cumsum(np.random.normal(0, 0.5, n_samples))
    base_signal = 50 + trend + daily_cycle + weekly_cycle + random_walk
    
    # 确保信号为正值
    y_true = np.maximum(base_signal, 1.0)
    
    # 创建6个模型的预测结果，模拟论文中的模型
    models_predictions = {}
    
    # CNN-BI-LSTM-AM (最好的模型)
    noise1 = np.random.normal(0, 1.2, n_samples)
    bias1 = 0.01 * y_true + np.sin(time_steps * 0.1) * 0.5
    models_predictions['CNN-BI-LSTM-AM'] = y_true + noise1 + bias1
    
    # CNN-LSTM-AM (第二好的模型)
    noise2 = np.random.normal(0, 1.8, n_samples)
    bias2 = -0.02 * y_true + np.cos(time_steps * 0.08) * 0.8
    models_predictions['CNN-LSTM-AM'] = y_true + noise2 + bias2
    
    # CNN-BI-LSTM (中等性能)
    noise3 = np.random.normal(0, 2.2, n_samples)
    bias3 = 0.03 * y_true + np.sin(time_steps * 0.12) * 1.2
    models_predictions['CNN-BI-LSTM'] = y_true + noise3 + bias3
    
    # CNN-LSTM (中等偏下性能)
    noise4 = np.random.normal(0, 2.8, n_samples)
    bias4 = -0.04 * y_true + np.cos(time_steps * 0.15) * 1.5
    models_predictions['CNN-LSTM'] = y_true + noise4 + bias4
    
    # CNN-RNN (较差性能)
    noise5 = np.random.normal(0, 3.2, n_samples)
    bias5 = 0.05 * y_true + np.sin(time_steps * 0.18) * 2.0
    models_predictions['CNN-RNN'] = y_true + noise5 + bias5
    
    # CNN-GRU (中等性能)
    noise6 = np.random.normal(0, 2.5, n_samples)
    bias6 = -0.025 * y_true + np.cos(time_steps * 0.1) * 1.0
    models_predictions['CNN-GRU'] = y_true + noise6 + bias6
    
    return y_true, models_predictions

def create_results_format(y_true, models_predictions):
    """转换为UniversalVisualizer格式"""
    results = {}
    
    for model_name, y_pred in models_predictions.items():
        # 计算评估指标
        mae = np.mean(np.abs(y_pred - y_true))
        mse = np.mean((y_pred - y_true) ** 2)
        r2 = 1 - np.sum((y_pred - y_true) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)
        
        results[model_name] = {
            'test': {
                'MAE': mae,
                'MSE': mse,
                'R2': r2
            },
            'predictions': {
                'test': (y_true, y_pred)
            }
        }
    
    return results

def main():
    """主函数"""
    print("🎯 论文风格相对百分比误差可视化演示")
    print("=" * 60)
    
    # 生成演示数据
    print("📊 生成演示数据...")
    y_true, models_predictions = create_paper_style_demo()
    
    # 转换格式
    results = create_results_format(y_true, models_predictions)
    
    print(f"✅ 生成了 {len(models_predictions)} 个模型的预测数据")
    print(f"📈 数据点数量: {len(y_true)}")
    
    # 显示模型性能概览
    print("\n📋 模型性能概览:")
    for model_name in models_predictions.keys():
        mae = results[model_name]['test']['MAE']
        r2 = results[model_name]['test']['R2']
        print(f"  {model_name:15s}: MAE={mae:.2f}, R²={r2:.3f}")
    
    # 创建可视化器
    visualizer = UniversalVisualizer()
    
    # 创建保存目录
    save_dir = os.path.join(get_current_paths()['figures'], 'paper_style_demo')
    os.makedirs(save_dir, exist_ok=True)
    
    # 生成论文风格的相对百分比误差图
    print("\n🎨 生成论文风格的相对百分比误差图...")
    
    try:
        filepath = visualizer._plot_relative_percentage_error_with_inset(
            results=results,
            dataset='test',
            save_dir=save_dir,
            experiment_name='Paper_Style_CNN_Models'
        )
        
        if filepath:
            print(f"✅ 论文风格图表已保存!")
            print(f"📁 文件路径: {filepath}")
            print(f"📊 文件名: {os.path.basename(filepath)}")
            
            # 显示图表特点
            print("\n🎯 图表特点:")
            print("  ✓ 多模型相对百分比误差对比")
            print("  ✓ 不同线型和颜色区分模型")
            print("  ✓ 插图显示局部细节")
            print("  ✓ 学术论文风格")
            print("  ✓ 高分辨率输出 (300 DPI)")
            
        else:
            print("❌ 图表生成失败")
            
    except Exception as e:
        print(f"❌ 生成图表时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n💡 使用建议:")
    print("  1. 这个图表特别适合展示多个模型的预测精度对比")
    print("  2. 相对百分比误差能够更直观地显示模型的相对性能")
    print("  3. 插图功能可以突出显示关键的时间段")
    print("  4. 可以直接用于学术论文或技术报告")
    
    print("\n🔧 集成到你的实验:")
    print("  • 在你的实验代码中调用 auto_visualize() 函数")
    print("  • 新的相对百分比误差图会自动生成")
    print("  • 图表保存在 error_analysis 文件夹中")
    
    print(f"\n🎉 演示完成! 请查看生成的图表: {os.path.basename(filepath) if filepath else '生成失败'}")

if __name__ == "__main__":
    main()
