"""
配置文件，包含模型参数、路径设置和可视化配置
"""

import os
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import platform
from datetime import datetime

# 尝试导入PyTorch，如果失败则使用CPU设备
try:
    import torch
    DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
except (ImportError, AttributeError):
    # 如果PyTorch未安装或模拟环境中，使用字符串表示设备
    DEVICE = 'cpu'
    print("警告: PyTorch未安装或在模拟环境中，使用CPU设备")

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def generate_timestamped_paths(base_timestamp: str = None) -> dict:
    """
    生成带时间戳的结果路径

    Args:
        base_timestamp: 基础时间戳字符串，如果为None则生成当前时间戳

    Returns:
        包含时间戳路径的字典
    """
    if base_timestamp is None:
        # 生成当前时间戳：YYYY-MM-DD_HH-MM-SS
        base_timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

    # 创建时间戳结果目录
    timestamped_results_dir = os.path.join(PROJECT_ROOT, 'results', base_timestamp)

    return {
        'raw_data': os.path.join(PROJECT_ROOT, 'data', 'raw'),
        'processed_data': os.path.join(PROJECT_ROOT, 'data', 'processed'),
        'results_root': timestamped_results_dir,  # 新增：时间戳根目录
        'models': os.path.join(timestamped_results_dir, 'models'),
        'figures': os.path.join(timestamped_results_dir, 'figures'),
        'reports': os.path.join(timestamped_results_dir, 'reports'),
        'timestamp': base_timestamp  # 新增：时间戳字符串
    }

# 默认数据路径（保持向后兼容）
DATA_PATHS = {
    'raw_data': os.path.join(PROJECT_ROOT, 'data', 'raw'),
    'processed_data': os.path.join(PROJECT_ROOT, 'data', 'processed'),
    'models': os.path.join(PROJECT_ROOT, 'results', 'models'),
    'figures': os.path.join(PROJECT_ROOT, 'results', 'figures'),
    'reports': os.path.join(PROJECT_ROOT, 'results', 'reports')
}

# 全局变量：当前训练会话的时间戳路径
CURRENT_TRAINING_PATHS = None

def ensure_directory_exists(directory_path: str, description: str = "") -> bool:
    """
    确保目录存在，使用多种方法尝试创建

    Args:
        directory_path: 目录路径
        description: 目录描述

    Returns:
        是否成功创建或已存在
    """
    if os.path.exists(directory_path):
        return True

    try:
        # 方法1：标准创建
        os.makedirs(directory_path, exist_ok=True)
        if os.path.exists(directory_path):
            print(f"✅ 创建目录成功 ({description}): {directory_path}")
            return True
    except Exception as e1:
        print(f"⚠️ 标准创建失败 ({description}): {e1}")

        try:
            # 方法2：逐级创建
            parts = []
            temp_path = directory_path
            while temp_path and temp_path != os.path.dirname(temp_path):
                parts.append(temp_path)
                temp_path = os.path.dirname(temp_path)
            parts.reverse()

            for part in parts:
                if not os.path.exists(part):
                    os.mkdir(part)

            if os.path.exists(directory_path):
                print(f"✅ 逐级创建成功 ({description}): {directory_path}")
                return True
        except Exception as e2:
            print(f"⚠️ 逐级创建失败 ({description}): {e2}")

            try:
                # 方法3：使用pathlib
                from pathlib import Path
                Path(directory_path).mkdir(parents=True, exist_ok=True)
                if os.path.exists(directory_path):
                    print(f"✅ pathlib创建成功 ({description}): {directory_path}")
                    return True
            except Exception as e3:
                print(f"❌ 所有方法都失败 ({description}): {e3}")
                return False

    return False

def setup_training_session(timestamp: str = None) -> dict:
    """
    设置新的训练会话，生成时间戳路径并创建目录

    Args:
        timestamp: 可选的时间戳字符串，如果为None则生成当前时间戳

    Returns:
        时间戳路径字典
    """
    global CURRENT_TRAINING_PATHS

    # 生成时间戳路径
    CURRENT_TRAINING_PATHS = generate_timestamped_paths(timestamp)

    print(f"🔧 正在设置训练会话...")
    print(f"📁 项目根目录: {PROJECT_ROOT}")
    print(f"⏰ 时间戳: {CURRENT_TRAINING_PATHS['timestamp']}")

    # 首先确保基础results目录存在
    base_results_dir = os.path.join(PROJECT_ROOT, 'results')
    if not ensure_directory_exists(base_results_dir, "基础results目录"):
        raise RuntimeError(f"无法创建基础results目录: {base_results_dir}")

    # 创建所有必要的目录
    directories_to_create = [
        ('results_root', CURRENT_TRAINING_PATHS['results_root']),
        ('models', CURRENT_TRAINING_PATHS['models']),
        ('figures', CURRENT_TRAINING_PATHS['figures']),
        ('reports', CURRENT_TRAINING_PATHS['reports'])
    ]

    print(f"📋 创建目录结构:")
    failed_directories = []

    for name, directory in directories_to_create:
        if not ensure_directory_exists(directory, name):
            failed_directories.append((name, directory))

    if failed_directories:
        print(f"❌ 以下目录创建失败:")
        for name, directory in failed_directories:
            print(f"   {name}: {directory}")
        raise RuntimeError("部分目录创建失败，请检查权限和路径")

    # 验证所有目录是否成功创建
    print(f"🔍 验证目录结构:")
    all_verified = True
    for name, directory in directories_to_create:
        if os.path.exists(directory) and os.path.isdir(directory):
            print(f"✅ {name}: 验证通过")
        else:
            print(f"❌ {name}: 验证失败 - {directory}")
            all_verified = False

    if not all_verified:
        raise RuntimeError("目录验证失败")

    print(f"✅ 训练会话已设置，时间戳: {CURRENT_TRAINING_PATHS['timestamp']}")
    print(f"📁 结果将保存到: {CURRENT_TRAINING_PATHS['results_root']}")

    return CURRENT_TRAINING_PATHS

def get_current_paths() -> dict:
    """
    获取当前训练会话的路径

    Returns:
        当前路径字典，如果未设置训练会话则自动设置
    """
    global CURRENT_TRAINING_PATHS
    if CURRENT_TRAINING_PATHS is None:
        print("⚠️  警告: 未设置训练会话，自动设置新的训练会话")
        setup_training_session()
    return CURRENT_TRAINING_PATHS

def reset_to_default_paths():
    """重置为默认路径（用于测试或特殊情况）"""
    global CURRENT_TRAINING_PATHS
    CURRENT_TRAINING_PATHS = None
    print("🔄 已重置为默认路径")

# 数据集配置
DATASET_CONFIG = {
    'data_file': 'Wind farm site 2 (Nominal capacity-200MW)_high_frequency_power.csv',  # 默认数据集文件名
    'target_column': 'Power (MW)',           # 目标变量列名
    'datetime_column': 'Time(year-month-day h:m:s)',  # 时间列名
    'feature_columns': [                     # 特征列名列表
        'Wind speed at height of 10 meters (m/s)',
        'Wind direction at height of 10 meters (˚)',
        'Wind speed at height of 30 meters (m/s)',
        'Wind direction at height of 30 meters (˚)',
        'Wind speed at height of 50 meters (m/s)',
        'Wind direction at height of 50 meters (˚)',
        'Wind speed - at the height of wheel hub  (m/s)',
        'Wind speed - at the height of wheel hub (˚)',
        'Air temperature  (°C) ',
        'Atmosphere (hpa)'
    ],
    'data_description': 'Wind farm site 2 (Nominal capacity-200MW)',  # 数据集描述
}

# 模型配置
MODEL_CONFIG = {
    'sequence_length': 24,
    'train_ratio': 0.8,
    'val_ratio': 0.10,
    'test_ratio': 0.10,
    'batch_size': 32,        # 平衡内存使用和训练稳定性
    'epochs': 80,            # 正式训练轮数
    'patience': 15,            # 增加耐心值，避免DPTAM过早停止
    'learning_rate': 0.0005,       # 稍微降低学习率，有利于DPTAM注意力机制的稳定学习
    'dropout_rate': 0.3,
    'device': DEVICE,
    'random_seed': 445
}

# GRU模型配置
GRU_CONFIG = {
    'name': 'GRU',
    'gru_units': [128,64],
    'dense_units': [64, 32]
}

# LSTM模型配置
LSTM_CONFIG = {
    'name': 'LSTM',
    'lstm_units': [128,64],
    'dense_units': [64, 32]
}

# BiGRU模型配置
BIGRU_CONFIG = {
    'name': 'BiGRU',
    'bigru_units': [20, 10],       # 基线模型：降低复杂度确保最基础性能
    'dense_units': [20, 10],       # 基线模型：降低全连接层容量
    'dropout_rate': 0.3,           # 基线模型：提高dropout限制性能
    'bidirectional': True          # 双向标志
}

# DPTAM-BiGRU融合模型配置
DPTAM_BIGRU_CONFIG = {
    'name': 'DPTAM-BiGRU',
    'n_segment': 6,                # DPTAM分段数：更细粒度时序建模（24小时分6段，每段4小时）
    'dptam_kernel_size': 3,        # DPTAM卷积核大小
    'bigru_units': [32,16],       # DPTAM融合模型：更大容量体现注意力优势
    'dense_units': [32,16],       # DPTAM融合模型：更大全连接层
    'dropout_rate': 0.3,          # DPTAM融合模型：较低dropout释放性能
    'bidirectional': True,         # 双向标志
    'fusion_strategy': 'serial'    # 融合策略: 'serial', 'parallel', 'multilevel'
}

# ASB-DPTAM-BiGRU融合模型配置（改进版）
ASB_DPTAM_BIGRU_CONFIG = {
    'name': 'ASB-DPTAM-BiGRU-Improved',
    'fusion_strategy': 'asb_dptam_serial_with_residual_fusion',  # 改进版串联架构

    # ASB (自适应频谱块) 配置
    'asb_adaptive_filter': True,           # 启用自适应滤波
    'asb_description': '频域特征增强，自适应噪声滤波',

    # DPTAM (时序注意力) 配置
    'n_segment': 6,                        # 分段数 (24小时分6段，每段4小时)
    'dptam_kernel_size': 3,                # DPTAM卷积核大小
    'dptam_description': '分段时序注意力机制',

    # BiGRU (双向GRU) 配置
    'bigru_units': [64, 32],               # 双向GRU层单元数 (恢复较大容量)
    'dense_units': [32, 16],               # 全连接层单元数
    'dropout_rate': 0.3,                  # Dropout率
    'bidirectional': True,                 # 双向标志
    'bigru_description': '双向时序特征提取',

    # 🆕 残差连接配置
    'residual_connections': True,          # 启用残差连接
    'residual_weight_init': 0.1,          # 残差权重初始值
    'residual_description': '可学习的残差连接权重，改善梯度流',

    # 🆕 特征融合配置
    'feature_fusion': True,               # 启用特征融合
    'fusion_dropout': 0.1,                # 融合层dropout率
    'fusion_activation': 'GELU',          # 融合层激活函数
    'fusion_normalization': 'LayerNorm',  # 融合层归一化方式
    'fusion_description': '多层特征融合，减少信息丢失',

    # 🆕 改进架构配置
    'improved_architecture': True,        # 标识为改进版架构
    'serial_processing': 'ASB+Fusion+Residual→DPTAM+Fusion+Residual→BiGRU',
    'architecture_description': 'ASB频域增强+特征融合+残差连接 → DPTAM时序注意力+特征融合+残差连接 → BiGRU双向建模',

    # 🆕 训练策略建议
    'training_strategy': {
        'learning_rate': 0.0003,           # 建议较低学习率
        'weight_decay': 1e-4,              # 权重衰减
        'warmup_epochs': 10,               # 预热轮数
        'scheduler': 'CosineAnnealing',    # 学习率调度器
        'patience': 15,                    # 早停耐心
    }
}

# BiLSTM模型配置
BILSTM_CONFIG = {
    'name': 'BiLSTM',
    'bilstm_units': [20, 10],      # 双向LSTM层的单元数
    'dense_units': [20, 10],        # 全连接层的单元数
    'dropout_rate': 0.4,            # Dropout比例
    'bidirectional': True           # 双向标志
}

# CNN-BiLSTM模型配置
CNN_BILSTM_CONFIG = {
    'name': 'CNN-BiLSTM',
    'cnn_filters': [20, 10],        # CNN卷积层的滤波器数量
    'cnn_kernel_sizes': [1, 1],     # CNN卷积核大小
    'bilstm_units': [20, 10],      # 双向LSTM层的单元数
    'dense_units': [30,15],        # 全连接层的单元数
    'dropout_rate': 0.4,            # Dropout比例
    'pool_size': 1                  # 池化层大小
}

# CNN-BiLSTM-Attention模型配置
CNN_BILSTM_ATTENTION_CONFIG = {
    'name': 'CNN-BiLSTM-Attention',
    'cnn_filters': [20, 10],        # CNN卷积层的滤波器数量
    'cnn_kernel_sizes': [1, 1],     # CNN卷积核大小
    'bilstm_units': [40, 20],      # 双向LSTM层的单元数
    'attention_units': 64,          # 注意力机制的隐藏单元数
    'dense_units': [20,10],        # 全连接层的单元数
    'dropout_rate': 0.4,            # Dropout比例
    'pool_size': 1                  # 池化层大小
}

# CNN-BiGRU模型配置
CNN_BIGRU_CONFIG = {
    'name': 'CNN-BiGRU',
    'cnn_filters': [20, 10],        # CNN卷积层的滤波器数量 (适度增加)
    'cnn_kernel_sizes': [1, 1],     # CNN卷积核大小
    'bigru_units': [20, 10],        # 双向GRU层的单元数 (适度增加)
    'dense_units': [20, 10],        # 全连接层的单元数 (适度增加)
    'dropout_rate': 0.4,            # Dropout比例 (适中)
    'pool_size': 1                  # 池化层大小
}

# CNN-BiGRU-Attention模型配置
CNN_BIGRU_ATTENTION_CONFIG = {
    'name': 'CNN-BiGRU-Attention',
    'cnn_filters': [20, 10],        # CNN卷积层的滤波器数量 (增加容量)
    'cnn_kernel_sizes': [1, 1],     # CNN卷积核大小 (保持)
    'bigru_units': [40, 20],        # 双向GRU层的单元数 (与DPTAM相同)
    'attention_units': 64,          # 注意力机制的隐藏单元数 (增加注意力容量)
    'dense_units': [20, 10],        # 全连接层的单元数 (增加容量)
    'dropout_rate': 0.4,            # Dropout比例 (降低限制)
    'pool_size': 1                  # 池化层大小
}


# ============================================================================
# 对比实验配置 - 统一管理所有对比实验参数
# ============================================================================

# 对比实验全局配置
COMPARISON_EXPERIMENT_CONFIG = {
    'name': 'ASB-DPTAM模型对比实验',
    'description': '对比分析ASB频域增强和DPTAM时序注意力机制的性能提升效果',

    # 实验控制参数 - 模型开关
    'enable_baseline_bigru': True,          # 是否训练基线BiGRU模型
    'enable_dptam_bigru': True,             # 是否训练DPTAM-BiGRU模型
    'enable_asb_dptam_bigru': True,         # 是否训练ASB-DPTAM-BiGRU模型
    'enable_bilstm': False,                  # 是否训练BiLSTM模型
    'enable_cnn_bilstm': False,              # 是否训练CNN-BiLSTM模型
    'enable_cnn_bilstm_attention': True,    # 是否训练CNN-BiLSTM-Attention模型
    'enable_cnn_bigru': False,               # 是否训练CNN-BiGRU模型
    'enable_cnn_bigru_attention': True,     # 是否训练CNN-BiGRU-Attention模型

    # 内存管理配置
    'memory_optimization': {
        'use_small_batch_evaluation': True,  # 是否使用小批量评估避免内存问题
        'train_samples_limit': 1000,         # 训练集评估样本数限制
        'val_samples_limit': 500,            # 验证集评估样本数限制
        'test_samples_limit': 500,           # 测试集评估样本数限制
        'fallback_train_limit': 200,        # 内存不足时的训练集样本数
        'fallback_val_limit': 100,          # 内存不足时的验证集样本数
        'fallback_test_limit': 100,         # 内存不足时的测试集样本数
        'analysis_samples_limit': 50,       # 注意力分析样本数限制
    },

    # 训练配置覆盖（如果需要与主配置不同）
    'training_overrides': {
        'epochs': 80,                        # 对比实验的训练轮数 (正式训练)
        'patience': 15,                      # 对比实验的早停耐心值
        'learning_rate': 0.0005,             # 对比实验的学习率
        'batch_size': 32,                    # 对比实验的批次大小
    },

    # 可视化配置
    'visualization': {
        'save_attention_analysis': True,     # 是否保存注意力分析图
        'save_comprehensive_analysis': True, # 是否保存综合分析图
        'figure_dpi': 300,                   # 图像分辨率
        'figure_format': 'png',              # 图像格式
    },

    # 报告生成配置
    'report': {
        'generate_detailed_report': True,    # 是否生成详细报告
        'include_model_architecture': True,  # 是否包含模型架构信息
        'include_attention_analysis': True,  # 是否包含注意力分析
        'include_performance_comparison': True, # 是否包含性能对比
    }
}

# 可视化配置
def setup_matplotlib():
    """设置matplotlib的中文字体和学术风格"""
    
    # 设置学术风格
    try:
        # 尝试使用新版本的seaborn样式
        plt.style.use('seaborn-v0_8-whitegrid')
    except OSError:
        try:
            # 尝试使用旧版本的seaborn样式
            plt.style.use('seaborn-whitegrid')
        except OSError:
            try:
                # 如果seaborn样式都不可用，使用matplotlib内置样式
                plt.style.use('whitegrid')
            except OSError:
                # 最后使用默认样式
                plt.style.use('default')
                print("警告: 无法加载seaborn样式，使用默认样式")
    
    # 解决中文字体问题
    system = platform.system()
    
    if system == 'Windows':
        # Windows系统字体
        fonts = ['SimHei', 'Microsoft YaHei', 'KaiTi', 'FangSong']
    elif system == 'Darwin':  # macOS
        fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans']
    
    # 尝试设置字体
    font_set = False
    for font in fonts:
        try:
            plt.rcParams['font.sans-serif'] = [font]
            font_set = True
            print(f"成功设置字体: {font}")
            break
        except:
            continue
    
    if not font_set:
        print("警告: 无法设置中文字体，将使用默认字体")
        plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置学术风格参数
    plt.rcParams.update({
        'figure.figsize': (10, 6),
        'figure.dpi': 300,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight',
        'savefig.pad_inches': 0.1,
        'font.size': 12,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 10,
        'lines.linewidth': 2,
        'lines.markersize': 6,
        'grid.alpha': 0.3,
        'axes.grid': True,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.edgecolor': 'gray',
        'axes.linewidth': 0.8
    })

# 鲜艳直观的颜色配置
ACADEMIC_COLORS = {
    'primary': '#1E88E5',      # 鲜艳蓝色
    'secondary': '#E91E63',    # 鲜艳粉红色
    'accent': '#FF9800',       # 鲜艳橙色
    'success': '#4CAF50',      # 鲜艳绿色
    'info': '#00BCD4',         # 鲜艳青色
    'warning': '#FFC107',      # 鲜艳黄色
    'light': '#F5F5F5',        # 浅灰色
    'dark': '#212121'          # 深黑色
}

# 更直观的模型颜色映射 - 支持更多模型类型
MODEL_COLORS = {
    'GRU': '#1E88E5',              # 鲜艳蓝色 - 更容易识别
    'LSTM': '#E91E63',             # 鲜艳粉红色 - 与蓝色形成强烈对比
    'BiGRU': '#4CAF50',            # 鲜艳绿色 - 双向GRU用绿色
    'DPTAM-BiGRU': '#FF9800',      # 鲜艳橙色 - DPTAM融合模型

    'Baseline-BiGRU': '#9C27B0',   # 紫色 - 基线BiGRU
    'Actual': '#212121',           # 深黑色 - 原始数据用黑色突出
    'True': '#212121',             # 深黑色 - 真实值（别名）
    'Original': '#212121'          # 深黑色 - 原始功率（别名）
}

# 自动颜色分配池 - 当模型不在预定义列表中时使用
AUTO_COLOR_POOL = [
    '#FF5722',  # 深橙红
    '#795548',  # 棕色
    '#607D8B',  # 蓝灰色
    '#3F51B5',  # 靛蓝
    '#009688',  # 青绿色
    '#FFC107',  # 琥珀色
    '#8BC34A',  # 浅绿色
    '#CDDC39',  # 柠檬绿
    '#FFEB3B',  # 黄色
    '#FF9C27'   # 深橙色
]

# 评估指标
METRICS = ['MAE', 'MSE', 'R2', 'MAPE']

# 特征列表
FEATURE_COLUMNS = [
    'Wind_speed_10m', 'Wind_direction_10m', 'Wind_speed_30m', 'Wind_direction_30m',
    'Wind_speed_50m', 'Wind_direction_50m', 'Wind_speed_hub', 'Wind_direction_hub',
    'Air_temperature', 'Atmosphere', 'Relative_humidity',
    'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos',
    'wind_speed_diff', 'wind_direction_diff',
    'Power_lag1', 'Power_lag2', 'Power_lag3',
    'Power_ma3', 'Power_ma6', 'Wind_speed_ma3'
]

# 初始化matplotlib设置
setup_matplotlib()

print("配置文件加载完成")
print(f"项目根目录: {PROJECT_ROOT}")
print(f"默认数据路径: {DATA_PATHS}")
print("\n💡 使用说明:")
print("  - 调用 setup_training_session() 开始新的训练会话")
print("  - 调用 get_current_paths() 获取当前路径")
print("  - 每次训练会自动生成时间戳文件夹，保留历史结果")
