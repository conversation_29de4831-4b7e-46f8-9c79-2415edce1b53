#!/usr/bin/env python3
"""
批量修改可视化标题和标签为英文，并修复R²显示问题
"""

import os
import re

def fix_visualization_file():
    """修复可视化文件中的中文标题和R²符号"""
    
    file_path = "src/utils/universal_visualizer.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义替换规则
    replacements = [
        # R²符号替换
        (r'R²', 'R^2'),
        
        # 标题替换
        (r"'模型训练损失对比'", "'Model Training Loss Comparison'"),
        (r"'模型训练MAE对比'", "'Model Training MAE Comparison'"),
        (r"'训练历史对比'", "'Training History Comparison'"),
        (r"'训练损失对比'", "'Training Loss Comparison'"),
        (r"'MAE指标对比'", "'MAE Metrics Comparison'"),
        (r"'评估指标对比'", "'Evaluation Metrics Comparison'"),
        (r"'RMSE性能对比'", "'RMSE Performance Comparison'"),
        (r"'R2性能对比'", "'R^2 Performance Comparison'"),
        (r"'MAE性能对比'", "'MAE Performance Comparison'"),
        (r"'误差分析'", "'Error Analysis'"),
        (r"'数据分布分析'", "'Data Distribution Analysis'"),
        (r"'模型性能雷达图'", "'Model Performance Radar Chart'"),
        (r"'综合时间序列预测对比'", "'Comprehensive Time Series Prediction Comparison'"),
        (r"'真实值分布'", "'True Values Distribution'"),
        (r"'预测值分布对比'", "'Predicted Values Distribution Comparison'"),
        
        # 文件名替换
        (r"'训练历史对比\.png'", "'Training_History_Comparison.png'"),
        (r"'训练损失对比\.png'", "'Training_Loss_Comparison.png'"),
        (r"'MAE指标对比\.png'", "'MAE_Metrics_Comparison.png'"),
        (r"'评估指标对比\.png'", "'Evaluation_Metrics_Comparison.png'"),
        (r"'RMSE性能对比\.png'", "'RMSE_Performance_Comparison.png'"),
        (r"'R²性能对比\.png'", "'R2_Performance_Comparison.png'"),
        (r"'MAE性能对比\.png'", "'MAE_Performance_Comparison.png'"),
        (r"'误差分析\.png'", "'Error_Analysis.png'"),
        (r"'数据分布分析\.png'", "'Data_Distribution_Analysis.png'"),
        (r"'综合时间序列预测对比\.png'", "'Comprehensive_Time_Series_Comparison.png'"),
        (r"'真实值分布\.png'", "'True_Values_Distribution.png'"),
        (r"'预测值分布对比\.png'", "'Predicted_Values_Distribution_Comparison.png'"),
        
        # 轴标签替换
        (r"'训练轮次 \(Epochs\)'", "'Epochs'"),
        (r"'损失值 \(MSE\)'", "'Loss (MSE)'"),
        (r"'平均绝对误差 \(MAE\)'", "'Mean Absolute Error (MAE)'"),
        (r"'训练轮数'", "'Epochs'"),
        (r"'损失值'", "'Loss'"),
        (r"'MAE值'", "'MAE'"),
        (r"'模型'", "'Models'"),
        (r"'时间步'", "'Time Steps'"),
        (r"'功率 \(MW\)'", "'Power (MW)'"),
        (r"'功率 \(标准化值\)'", "'Power (Normalized)'"),
        (r"'预测误差 \(标准化值\)'", "'Prediction Error (Normalized)'"),
        (r"'预测误差 \(MW\)'", "'Prediction Error (MW)'"),
        (r"'真实值 \(MW\)'", "'True Values (MW)'"),
        (r"'预测值 \(MW\)'", "'Predicted Values (MW)'"),
        (r"'功率值'", "'Power Values'"),
        (r"'频次'", "'Frequency'"),
        (r"'密度'", "'Density'"),
        (r"'指标值'", "'Metric Values'"),
        (r"'残差值'", "'Residual Values'"),
        (r"'真实值'", "'True Values'"),
        (r"'预测值'", "'Predicted Values'"),
        (r"'预测误差'", "'Prediction Error'"),
        
        # 图例标签替换
        (r"f'\{model_name\} 训练损失'", "f'{model_name} Training Loss'"),
        (r"f'\{model_name\} 验证损失'", "f'{model_name} Validation Loss'"),
        (r"f'\{model_name\} 训练MAE'", "f'{model_name} Training MAE'"),
        (r"f'\{model_name\} 验证MAE'", "f'{model_name} Validation MAE'"),
        (r"f'\{model_name\} - 训练损失'", "f'{model_name} - Training Loss'"),
        (r"f'\{model_name\} - 验证损失'", "f'{model_name} - Validation Loss'"),
        (r"f'\{model_name\} - 训练MAE'", "f'{model_name} - Training MAE'"),
        (r"f'\{model_name\} - 验证MAE'", "f'{model_name} - Validation MAE'"),
        (r"f'\{model_name\}预测值'", "f'{model_name} Predictions'"),
        (r"f'\{model_name\}预测误差'", "f'{model_name} Prediction Error'"),
        (r"f'\{model_name\}残差'", "f'{model_name} Residuals'"),
        (r"'原始功率'", "'Original Power'"),
        (r"'完美预测'", "'Perfect Prediction'"),
        (r"'完美预测线'", "'Perfect Prediction Line'"),
        (r"'训练损失'", "'Training Loss'"),
        (r"'验证损失'", "'Validation Loss'"),
        (r"'训练MAE'", "'Training MAE'"),
        (r"'验证MAE'", "'Validation MAE'"),
        
        # 数据集标签替换
        (r"f'\{dataset\.capitalize\(\)\}集'", "f'{dataset.capitalize()} Set'"),
        (r"f'\{dataset\.upper\(\)\}集预测结果对比'", "f'{dataset.upper()} Set Prediction Results Comparison'"),
        (r"f'\{dataset\.upper\(\)\}集整体预测对比'", "f'{dataset.upper()} Set Overall Prediction Comparison'"),
        (r"f'\{dataset\.upper\(\)\}集预测细节对比'", "f'{dataset.upper()} Set Prediction Details Comparison'"),
        
        # 子图标题替换
        (r"f'\{model_name\} - 预测 vs 真实值'", "f'{model_name} - Predictions vs True Values'"),
        (r"f'\{model_name\} - 时间序列预测对比'", "f'{model_name} - Time Series Prediction Comparison'"),
        (r"f'\{model_name\} - 误差分布'", "f'{model_name} - Error Distribution'"),
        (r"f'\{model_name\} - 误差 vs 真实值'", "f'{model_name} - Error vs True Values'"),
        (r"f'\{model_name\} - 预测误差分布'", "f'{model_name} - Prediction Error Distribution'"),
        (r"f'\{model_name\} - 预测值vs真实值'", "f'{model_name} - Predictions vs True Values'"),
        (r"f'\{model_name\} - 训练损失'", "f'{model_name} - Training Loss'"),
        (r"f'\{model_name\} - 训练MAE'", "f'{model_name} - Training MAE'"),
        (r"f'\{model_name\} - 时间序列预测'", "f'{model_name} - Time Series Prediction'"),
        (r"f'\{model_name\} - 性能指标'", "f'{model_name} - Performance Metrics'"),
        (r"f'\{model_name\} - 损失函数'", "f'{model_name} - Loss Function'"),
        (r"f'\{model_name\} - MAE指标'", "f'{model_name} - MAE Metrics'"),
        (r"f'\{model_name\} - 训练历史'", "f'{model_name} - Training History'"),
        (r"f'\{model_name\}模型完整分析'", "f'{model_name} Model Complete Analysis'"),
        (r"f'\{model_name\}_训练历史\.png'", "f'{model_name}_Training_History.png'"),
        (r"f'\{model_name\}_时间序列预测\.png'", "f'{model_name}_Time_Series_Prediction.png'"),
        (r"f'\{model_name\}_性能指标\.png'", "f'{model_name}_Performance_Metrics.png'"),
        
        # 其他标题
        (r"'风电功率预测综合对比'", "'Wind Power Prediction Comprehensive Comparison'"),
        (r"'预测误差对比'", "'Prediction Error Comparison'"),
        (r"'Q-Q图 \(正态性检验\)'", "'Q-Q Plot (Normality Test)'"),
        (r"'残差分布对比'", "'Residual Distribution Comparison'"),
        (r"f'\{model_name\} - 测试集性能指标'", "f'{model_name} - Test Set Performance Metrics'"),
        
        # 统计信息文本
        (r"'平均绝对误差:'", "'Mean Absolute Error:'"),
        (r"'均值:'", "'Mean:'"),
        (r"'标准差:'", "'Std Dev:'"),
    ]
    
    # 应用所有替换
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 可视化标题和标签已修改为英文")
    print("✅ R²符号已修改为R^2")

if __name__ == "__main__":
    fix_visualization_file()
    print("🎉 修复完成！")
