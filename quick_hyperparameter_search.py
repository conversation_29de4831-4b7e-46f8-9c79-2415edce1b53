#!/usr/bin/env python3
"""
ASB-DPTAM-BiGRU快速超参数搜索
基于现有的对比实验框架，专门优化4个关键超参数
"""

import os
import sys
import json
import itertools
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 修改配置文件中的参数，然后运行对比实验
from src.utils import config
from src.experiments.asb_dptam_advantage_comparison import ASBDPTAMAdvantageComparator

class QuickHyperparameterSearch:
    """快速超参数搜索器"""
    
    def __init__(self):
        self.search_results = []
        self.best_rmse = float('inf')
        self.best_params = None
        
        # 搜索空间
        self.search_space = {
            'n_segment': [4, 6, 8],
            'dptam_kernel_size': [3, 5],
            'bigru_units': [[32,16], [64,32]],
            'dense_units': [[16,8], [32,16]]
        }
        
        print("🎯 快速超参数搜索")
        print(f"📊 搜索空间: {self.search_space}")
        total_combinations = 1
        for key, values in self.search_space.items():
            total_combinations *= len(values)
        print(f"🔢 总组合数: {total_combinations}")
    
    def update_config_and_run(self, params):
        """更新配置并运行实验"""
        try:
            print(f"\n🔄 测试参数组合: {params}")
            
            # 备份原始配置
            original_config = config.ASB_DPTAM_BIGRU_CONFIG.copy()
            
            # 更新配置
            config.ASB_DPTAM_BIGRU_CONFIG.update({
                'n_segment': params['n_segment'],
                'dptam_kernel_size': params['dptam_kernel_size'],
                'bigru_units': params['bigru_units'],
                'dense_units': params['dense_units']
            })
            
            # 同时更新DPTAM_BIGRU_CONFIG以保持一致性
            config.DPTAM_BIGRU_CONFIG.update({
                'n_segment': params['n_segment'],
                'dptam_kernel_size': params['dptam_kernel_size'],
                'bigru_units': params['bigru_units'],
                'dense_units': params['dense_units']
            })
            
            print(f"   📝 已更新配置: {params}")
            
            # 创建对比实验实例
            comparator = ASBDPTAMAdvantageComparator()
            
            # 只训练ASB-DPTAM-BiGRU模型（跳过其他模型加速）
            print("   🚀 开始训练ASB-DPTAM-BiGRU模型...")
            
            # 加载数据
            data = comparator.load_and_prepare_data()
            
            # 只训练ASB-DPTAM-BiGRU
            comparator.train_asb_dptam_bigru(data)
            
            # 获取结果
            if 'ASB-DPTAM-BiGRU' in comparator.results:
                result = comparator.results['ASB-DPTAM-BiGRU']
                rmse = result['test_rmse']
                print(f"   ✅ RMSE: {rmse:.6f}")
                
                # 恢复原始配置
                config.ASB_DPTAM_BIGRU_CONFIG = original_config
                
                return rmse
            else:
                print("   ❌ 未找到训练结果")
                config.ASB_DPTAM_BIGRU_CONFIG = original_config
                return float('inf')
                
        except Exception as e:
            print(f"   ❌ 训练失败: {str(e)}")
            # 恢复原始配置
            config.ASB_DPTAM_BIGRU_CONFIG = original_config
            return float('inf')
    
    def grid_search(self):
        """执行网格搜索"""
        print("\n🔍 开始网格搜索...")
        
        # 生成所有参数组合
        keys = list(self.search_space.keys())
        values = list(self.search_space.values())
        combinations = list(itertools.product(*values))
        
        total_combinations = len(combinations)
        print(f"🎯 总共需要测试 {total_combinations} 个组合")
        
        for i, combo in enumerate(combinations):
            params = dict(zip(keys, combo))
            
            print(f"\n📋 进度: {i+1}/{total_combinations}")
            
            # 训练并评估
            rmse = self.update_config_and_run(params)
            
            # 记录结果
            result = {
                'trial': i+1,
                'params': params,
                'rmse': rmse,
                'timestamp': datetime.now().isoformat()
            }
            self.search_results.append(result)
            
            # 更新最佳结果
            if rmse < self.best_rmse:
                self.best_rmse = rmse
                self.best_params = params.copy()
                print(f"🎉 发现新的最佳结果!")
                print(f"   RMSE: {rmse:.6f}")
                print(f"   参数: {params}")
                
                # 保存当前最佳结果
                self.save_current_best()
        
        return self.best_params, self.best_rmse
    
    def save_current_best(self):
        """保存当前最佳结果"""
        best_result = {
            'best_params': self.best_params,
            'best_rmse': self.best_rmse,
            'timestamp': datetime.now().isoformat()
        }
        
        with open('current_best_hyperparams.json', 'w', encoding='utf-8') as f:
            json.dump(best_result, f, indent=2, ensure_ascii=False)
        
        print(f"💾 当前最佳结果已保存到: current_best_hyperparams.json")
    
    def save_all_results(self):
        """保存所有搜索结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hyperparameter_search_results_{timestamp}.json"
        
        results = {
            'best_params': self.best_params,
            'best_rmse': self.best_rmse,
            'all_results': self.search_results,
            'search_space': self.search_space,
            'total_trials': len(self.search_results)
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 完整结果已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("🚀 ASB-DPTAM-BiGRU快速超参数搜索")
    print("=" * 60)
    
    searcher = QuickHyperparameterSearch()
    
    try:
        # 执行搜索
        best_params, best_rmse = searcher.grid_search()
        
        # 显示最终结果
        print("\n" + "=" * 60)
        print("🏆 搜索完成!")
        print(f"🎯 最佳RMSE: {best_rmse:.6f}")
        print(f"🔧 最佳参数:")
        for key, value in best_params.items():
            print(f"   {key}: {value}")
        
        # 保存完整结果
        searcher.save_all_results()
        
        # 显示如何应用最佳参数
        print(f"\n📋 如何应用最佳参数:")
        print(f"在 src/utils/config.py 中更新 ASB_DPTAM_BIGRU_CONFIG:")
        print(f"'n_segment': {best_params['n_segment']},")
        print(f"'dptam_kernel_size': {best_params['dptam_kernel_size']},")
        print(f"'bigru_units': {best_params['bigru_units']},")
        print(f"'dense_units': {best_params['dense_units']},")
        
    except KeyboardInterrupt:
        print("\n⚠️ 搜索被用户中断")
        if searcher.best_params:
            print(f"当前最佳结果: RMSE={searcher.best_rmse:.6f}")
            print(f"当前最佳参数: {searcher.best_params}")
        searcher.save_all_results()
    
    except Exception as e:
        print(f"\n❌ 搜索过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        searcher.save_all_results()

if __name__ == "__main__":
    main()
