#!/usr/bin/env python3
"""
测试英文可视化功能
验证所有图表标题和标签都是英文，R^2显示正确
"""

import os
import sys

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.universal_visualizer import UniversalVisualizer
from src.utils.config import setup_matplotlib, get_current_paths

def create_test_data():
    """创建测试数据"""
    print("📊 生成英文可视化测试数据...")
    
    n_samples = 100
    time_steps = np.arange(n_samples)
    
    # 生成基础信号
    daily_cycle = 15 * np.sin(2 * np.pi * time_steps / 24)
    trend = 0.05 * time_steps
    random_noise = np.random.normal(0, 2, n_samples)
    
    base_power = 40 + daily_cycle + trend + random_noise
    y_true = np.maximum(base_power, 1.0)
    
    # 创建模型预测结果
    models_predictions = {}
    
    # BiGRU
    noise = np.random.normal(0, 2.5, n_samples)
    bias = 0.04 * y_true + np.sin(time_steps * 0.15) * 1.5
    models_predictions['BiGRU'] = y_true + noise + bias
    
    # DPTAM-BiGRU
    noise = np.random.normal(0, 1.8, n_samples)
    bias = 0.03 * y_true + np.cos(time_steps * 0.12) * 1.2
    models_predictions['DPTAM-BiGRU'] = y_true + noise + bias
    
    # ASB-DPTAM-BiGRU
    noise = np.random.normal(0, 1.2, n_samples)
    bias = 0.02 * y_true + np.sin(time_steps * 0.08) * 0.8
    models_predictions['ASB-DPTAM-BiGRU'] = y_true + noise + bias
    
    return y_true, models_predictions

def create_results_format(y_true, models_predictions):
    """转换为UniversalVisualizer格式"""
    results = {}
    
    for model_name, y_pred in models_predictions.items():
        mae = np.mean(np.abs(y_pred - y_true))
        mse = np.mean((y_pred - y_true) ** 2)
        r2 = 1 - np.sum((y_pred - y_true) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)
        
        results[model_name] = {
            'test': {
                'MAE': mae,
                'MSE': mse,
                'R^2': r2,  # 使用R^2而不是R²
                'RMSE': np.sqrt(mse)
            },
            'predictions': {
                'test': (y_true, y_pred)
            }
        }
    
    return results

def create_histories():
    """创建训练历史数据"""
    histories = {}
    
    for model_name in ['BiGRU', 'DPTAM-BiGRU', 'ASB-DPTAM-BiGRU']:
        epochs = 50
        # 模拟训练历史
        train_loss = [0.1 * np.exp(-i/20) + np.random.normal(0, 0.01) for i in range(epochs)]
        val_loss = [0.12 * np.exp(-i/18) + np.random.normal(0, 0.015) for i in range(epochs)]
        train_mae = [0.3 * np.exp(-i/25) + np.random.normal(0, 0.02) for i in range(epochs)]
        val_mae = [0.35 * np.exp(-i/22) + np.random.normal(0, 0.025) for i in range(epochs)]
        
        histories[model_name] = {
            'train_loss': train_loss,
            'val_loss': val_loss,
            'train_mae': train_mae,
            'val_mae': val_mae
        }
    
    return histories

def main():
    """主函数"""
    print("🎯 英文可视化功能测试")
    print("=" * 60)
    print("✨ 验证：所有标题和标签都是英文，R^2显示正确")
    print()
    
    # 生成测试数据
    y_true, models_predictions = create_test_data()
    results = create_results_format(y_true, models_predictions)
    histories = create_histories()
    
    print(f"✅ 生成了 {len(models_predictions)} 个模型的测试数据")
    print(f"📈 数据点数量: {len(y_true)}")
    
    # 显示模型性能（验证R^2显示）
    print("\n📋 模型性能概览（验证R^2显示）:")
    print("-" * 50)
    for model_name in models_predictions.keys():
        mae = results[model_name]['test']['MAE']
        r2 = results[model_name]['test']['R^2']
        print(f"  {model_name:20s}: MAE={mae:.3f}, R^2={r2:.3f}")
    
    # 创建可视化器
    visualizer = UniversalVisualizer()
    
    # 创建保存目录
    save_dir = os.path.join(get_current_paths()['figures'], 'english_visualization_test')
    os.makedirs(save_dir, exist_ok=True)
    
    print("\n🎨 生成英文可视化图表...")
    
    try:
        # 生成完整的可视化套件
        saved_files = visualizer.generate_complete_visualization_suite(
            results=results,
            histories=histories,
            save_path=save_dir,
            experiment_name='English_Visualization_Test'
        )
        
        if saved_files:
            print(f"✅ 英文可视化套件生成成功!")
            print(f"📊 共生成 {len(saved_files)} 个图表")
            print(f"📁 保存目录: {save_dir}")
            
            print("\n🎯 验证要点:")
            print("  ✓ 所有图表标题都是英文")
            print("  ✓ 所有轴标签都是英文")
            print("  ✓ 所有图例都是英文")
            print("  ✓ R^2 符号显示正确（不是R²）")
            print("  ✓ 文件名使用英文和下划线")
            
            print("\n📋 生成的图表文件:")
            for i, filepath in enumerate(saved_files, 1):
                filename = os.path.basename(filepath)
                print(f"  {i:2d}. {filename}")
                
        else:
            print("❌ 图表生成失败")
            
    except Exception as e:
        print(f"❌ 生成图表时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n💡 英文化的优势:")
    print("-" * 50)
    print("  • 符合国际学术标准")
    print("  • 适合国际期刊发表")
    print("  • 避免字体兼容性问题")
    print("  • 提高图表的专业性")
    
    print("\n🔧 主要修改:")
    print("-" * 50)
    print("  • 所有中文标题 → 英文标题")
    print("  • 所有中文标签 → 英文标签")
    print("  • R² → R^2 (避免方块字问题)")
    print("  • 中文文件名 → 英文文件名")
    
    print(f"\n🎉 英文可视化测试完成!")

if __name__ == "__main__":
    main()
