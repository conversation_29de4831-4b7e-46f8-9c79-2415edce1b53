#!/usr/bin/env python3
"""
将相对百分比误差可视化集成到你现有实验的示例
展示如何在 ASB-DPTAM 优势对比实验中使用新的可视化功能
"""

import os
import sys

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.visualization_manager import auto_visualize
from src.utils.universal_visualizer import UniversalVisualizer
from src.utils.config import get_current_paths

def example_integration_with_your_experiments():
    """
    示例：如何将相对百分比误差可视化集成到你的现有实验中
    """
    print("🔧 集成相对百分比误差可视化到你的实验")
    print("=" * 60)
    
    print("📋 方法1: 自动集成（推荐）")
    print("-" * 40)
    print("在你的实验脚本中，相对百分比误差图会自动生成：")
    print()
    
    # 展示代码示例
    code_example_1 = '''
# 在你的 asb_dptam_advantage_comparison.py 中
from src.utils.visualization_manager import auto_visualize

class ASBDPTAMAdvantageComparator:
    def run_comparison_experiment(self):
        # ... 你的训练代码 ...
        
        # 准备模型数据
        models_data = {
            'BiGRU': (self.results['BiGRU'], self.histories['BiGRU']),
            'DPTAM-BiGRU': (self.results['DPTAM-BiGRU'], self.histories['DPTAM-BiGRU']),
            'ASB-DPTAM-BiGRU': (self.results['ASB-DPTAM-BiGRU'], self.histories['ASB-DPTAM-BiGRU'])
        }
        
        # 自动生成所有可视化（包括新的相对百分比误差图）
        generated_files = auto_visualize(
            models_data=models_data,
            experiment_name="ASB_DPTAM_Advantage_Analysis"
        )
        
        # 新的相对百分比误差图会自动保存在 error_analysis 文件夹中
        print("✅ 相对百分比误差图已自动生成！")
    '''
    
    print(code_example_1)
    
    print("\n📋 方法2: 手动调用特定图表")
    print("-" * 40)
    print("如果你只想生成相对百分比误差图：")
    print()
    
    code_example_2 = '''
# 手动生成相对百分比误差图
from src.utils.universal_visualizer import UniversalVisualizer

def generate_relative_error_plot(self):
    """生成相对百分比误差图"""
    visualizer = UniversalVisualizer()
    
    # 准备结果数据
    results = {
        'BiGRU': self.results['BiGRU'],
        'DPTAM-BiGRU': self.results['DPTAM-BiGRU'], 
        'ASB-DPTAM-BiGRU': self.results['ASB-DPTAM-BiGRU']
    }
    
    # 生成图表
    save_dir = os.path.join(get_current_paths()['figures'], 'error_analysis')
    os.makedirs(save_dir, exist_ok=True)
    
    filepath = visualizer._plot_relative_percentage_error_with_inset(
        results=results,
        dataset='test',
        save_dir=save_dir,
        experiment_name='ASB_DPTAM_RelativeError'
    )
    
    return filepath
    '''
    
    print(code_example_2)
    
    print("\n📋 方法3: 扩展现有实验类")
    print("-" * 40)
    print("在你的实验类中添加新的可视化方法：")
    print()
    
    code_example_3 = '''
# 在 ASBDPTAMAdvantageComparator 类中添加方法
class ASBDPTAMAdvantageComparator:
    
    def generate_paper_style_visualizations(self):
        """生成论文风格的可视化图表"""
        print("🎨 生成论文风格的可视化图表...")
        
        # 1. 生成标准可视化套件
        models_data = {
            'BiGRU': (self.results['BiGRU'], self.histories['BiGRU']),
            'DPTAM-BiGRU': (self.results['DPTAM-BiGRU'], self.histories['DPTAM-BiGRU']),
            'ASB-DPTAM-BiGRU': (self.results['ASB-DPTAM-BiGRU'], self.histories['ASB-DPTAM-BiGRU'])
        }
        
        generated_files = auto_visualize(
            models_data=models_data,
            experiment_name="ASB_DPTAM_Paper_Analysis"
        )
        
        # 2. 查找相对百分比误差图
        relative_error_files = [f for f in generated_files['universal'] 
                               if 'relative_percentage_error' in f]
        
        if relative_error_files:
            print(f"✅ 论文风格相对百分比误差图: {relative_error_files[0]}")
        
        return generated_files
    '''
    
    print(code_example_3)

def show_expected_output():
    """展示预期的输出效果"""
    print("\n🎯 预期的可视化效果")
    print("=" * 60)
    
    effects = [
        "📊 **主图特点**:",
        "  • Y轴: 相对百分比误差 (Relative percentage error)",
        "  • X轴: 时间 (Time)",
        "  • 多条线: BiGRU, DPTAM-BiGRU, ASB-DPTAM-BiGRU",
        "  • 不同线型: 实线、虚线、点线等",
        "  • 颜色区分: 每个模型不同颜色",
        "",
        "🔍 **插图特点**:",
        "  • 位置: 图表左上角",
        "  • 内容: 70%-90%时间段的放大视图",
        "  • 标记: 主图中有灰色区域标记",
        "  • 箭头: 指向插图区域",
        "",
        "📈 **性能对比**:",
        "  • BiGRU: 基线性能，误差较大",
        "  • DPTAM-BiGRU: 加入注意力，误差减小",
        "  • ASB-DPTAM-BiGRU: 最完整模型，误差最小",
        "",
        "💾 **文件保存**:",
        "  • 位置: results/时间戳/figures/error_analysis/",
        "  • 格式: PNG, 300 DPI",
        "  • 命名: 实验名_relative_percentage_error_with_inset_test.png"
    ]
    
    for effect in effects:
        print(effect)

def integration_checklist():
    """集成检查清单"""
    print("\n✅ 集成检查清单")
    print("=" * 60)
    
    checklist = [
        "□ 确保你的模型结果包含 'predictions' 字段",
        "□ 预测结果格式: {'test': (y_true, y_pred)}",
        "□ 在实验脚本中调用 auto_visualize() 函数",
        "□ 检查 error_analysis 文件夹中的新图表",
        "□ 验证图表显示了所有模型的对比",
        "□ 确认插图功能正常工作",
        "□ 检查图表质量适合论文发表"
    ]
    
    for item in checklist:
        print(f"  {item}")

def main():
    """主函数"""
    print("🎨 相对百分比误差可视化 - 集成指南")
    print("=" * 60)
    print("基于你的 ASB-DPTAM-BiGRU 模型的可视化集成")
    print()
    
    example_integration_with_your_experiments()
    show_expected_output()
    integration_checklist()
    
    print("\n🚀 下一步操作:")
    print("  1. 运行你的 asb_dptam_advantage_comparison.py 实验")
    print("  2. 新的相对百分比误差图会自动生成")
    print("  3. 查看 error_analysis 文件夹中的图表")
    print("  4. 享受论文级别的可视化效果！")
    
    print("\n💡 提示:")
    print("  • 相对百分比误差图特别适合展示模型改进的效果")
    print("  • ASB-DPTAM-BiGRU 应该显示最小的误差波动")
    print("  • 插图可以突出显示模型差异最明显的时间段")

if __name__ == "__main__":
    main()
