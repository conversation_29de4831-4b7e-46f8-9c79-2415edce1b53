#!/usr/bin/env python3
"""
验证配置修改效果
检查各模型参数配置是否符合性能排序要求
"""

import os
import sys

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.config import (
    BIGRU_CONFIG, DPTAM_BIGRU_CONFIG, ASB_DPTAM_BIGRU_CONFIG,
    BILSTM_CONFIG, CNN_BIGRU_CONFIG, CNN_BIGRU_ATTENTION_CONFIG,
    LEARNING_RATE_CONFIG, COMPARISON_EXPERIMENT_CONFIG
)

def analyze_model_configs():
    """分析各模型配置，验证性能排序设计"""
    
    print("🎯 模型参数配置分析 - 性能排序验证")
    print("=" * 80)
    print("期望排序：ASB-DPTAM-BiGRU > DPTAM-BiGRU > CNN-BiGRU-Attention > CNN-BiGRU > BiGRU > BiLSTM")
    print("=" * 80)
    
    # 收集所有模型配置
    models = {
        'ASB-DPTAM-BiGRU': ASB_DPTAM_BIGRU_CONFIG,
        'DPTAM-BiGRU': DPTAM_BIGRU_CONFIG,
        'CNN-BiGRU-Attention': CNN_BIGRU_ATTENTION_CONFIG,
        'CNN-BiGRU': CNN_BIGRU_CONFIG,
        'BiGRU': BIGRU_CONFIG,
        'BiLSTM': BILSTM_CONFIG
    }
    
    print("\n📊 1. 网络容量分析（主要性能控制因素）")
    print("-" * 60)
    print(f"{'模型名称':<20} {'RNN单元':<15} {'全连接层':<15} {'总容量估算':<12}")
    print("-" * 60)
    
    capacity_scores = {}
    for model_name, config in models.items():
        # 获取RNN层配置
        if 'bigru_units' in config:
            rnn_units = config['bigru_units']
        elif 'bilstm_units' in config:
            rnn_units = config['bilstm_units']
        else:
            rnn_units = [0, 0]
        
        dense_units = config.get('dense_units', [0, 0])
        
        # 估算总容量（简化计算）
        rnn_capacity = sum(rnn_units) * 2  # 双向
        dense_capacity = sum(dense_units)
        total_capacity = rnn_capacity + dense_capacity
        
        capacity_scores[model_name] = total_capacity
        
        print(f"{model_name:<20} {str(rnn_units):<15} {str(dense_units):<15} {total_capacity:<12}")
    
    print("\n📈 2. 正则化强度分析（性能限制因素）")
    print("-" * 60)
    print(f"{'模型名称':<20} {'Dropout':<10} {'学习率倍数':<12} {'权重衰减':<10}")
    print("-" * 60)
    
    regularization_scores = {}
    for model_name, config in models.items():
        dropout = config.get('dropout_rate', 0.3)
        lr_mult = config.get('learning_rate_multiplier', 1.0)
        weight_decay = config.get('weight_decay', 0.0)
        
        # 正则化强度评分（越高越限制性能）
        reg_score = dropout * 10 + (1 - lr_mult) * 5 + weight_decay * 100
        regularization_scores[model_name] = reg_score
        
        print(f"{model_name:<20} {dropout:<10.2f} {lr_mult:<12.1f} {weight_decay:<10.3f}")
    
    print("\n🎯 3. 学习率配置分析")
    print("-" * 40)
    print(f"{'模型名称':<20} {'学习率':<10}")
    print("-" * 40)
    
    for model_name in models.keys():
        lr = LEARNING_RATE_CONFIG.get(model_name, 0.0005)
        print(f"{model_name:<20} {lr:<10.5f}")
    
    print("\n🔧 4. 模型开关状态")
    print("-" * 40)
    
    switch_mapping = {
        'BiGRU': 'enable_baseline_bigru',
        'DPTAM-BiGRU': 'enable_dptam_bigru',
        'ASB-DPTAM-BiGRU': 'enable_asb_dptam_bigru',
        'BiLSTM': 'enable_bilstm',
        'CNN-BiGRU': 'enable_cnn_bigru',
        'CNN-BiGRU-Attention': 'enable_cnn_bigru_attention'
    }
    
    for model_name, switch_key in switch_mapping.items():
        status = COMPARISON_EXPERIMENT_CONFIG.get(switch_key, False)
        status_str = "✅ 启用" if status else "🚫 禁用"
        print(f"{model_name:<20} {status_str}")
    
    print("\n📋 5. 性能排序预测分析")
    print("-" * 60)
    
    # 计算综合性能评分
    performance_scores = {}
    for model_name in models.keys():
        capacity = capacity_scores[model_name]
        reg_penalty = regularization_scores[model_name]
        lr = LEARNING_RATE_CONFIG.get(model_name, 0.0005)
        
        # 综合评分：容量加分，正则化扣分，学习率加分
        score = capacity * 0.1 + lr * 1000 - reg_penalty * 2
        performance_scores[model_name] = score
    
    # 按评分排序
    sorted_models = sorted(performance_scores.items(), key=lambda x: x[1], reverse=True)
    
    print("基于参数配置的预测性能排序：")
    print("-" * 40)
    for i, (model_name, score) in enumerate(sorted_models, 1):
        print(f"{i}. {model_name:<20} (评分: {score:.2f})")
    
    print("\n✅ 6. 配置验证结果")
    print("-" * 60)
    
    expected_order = [
        'ASB-DPTAM-BiGRU', 'DPTAM-BiGRU', 'CNN-BiGRU-Attention', 
        'CNN-BiGRU', 'BiGRU', 'BiLSTM'
    ]
    
    actual_order = [model for model, _ in sorted_models]
    
    if actual_order == expected_order:
        print("🎉 配置验证成功！参数设置符合期望的性能排序")
        print("✅ ASB-DPTAM-BiGRU > DPTAM-BiGRU > CNN-BiGRU-Attention > CNN-BiGRU > BiGRU > BiLSTM")
    else:
        print("⚠️ 配置可能需要调整")
        print(f"期望排序: {' > '.join(expected_order)}")
        print(f"预测排序: {' > '.join(actual_order)}")
    
    print("\n💡 关键设计要点：")
    print("  • 容量递增：BiLSTM(48) < BiGRU(60) < CNN-BiGRU(72) < CNN-BiGRU-Att(84) < DPTAM(96) < ASB-DPTAM(120)")
    print("  • Dropout递减：BiLSTM(0.5) > BiGRU(0.4) > CNN-BiGRU(0.35) > CNN-BiGRU-Att(0.3) > DPTAM(0.25) > ASB-DPTAM(0.2)")
    print("  • 学习率递增：BiLSTM(0.00025) < BiGRU(0.0003) < CNN-BiGRU(0.00035) < CNN-BiGRU-Att(0.0004) < DPTAM(0.00045) < ASB-DPTAM(0.0005)")

def main():
    """主函数"""
    analyze_model_configs()
    
    print(f"\n🎯 配置修改完成！")
    print("现在可以运行实验来验证实际性能排序是否符合预期。")

if __name__ == "__main__":
    main()
