#!/usr/bin/env python3
"""
简化版相对百分比误差可视化演示
时间步长100，无插图，基于你的实际模型
"""

import os
import sys

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import numpy as np
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.universal_visualizer import UniversalVisualizer
from src.utils.config import setup_matplotlib, get_current_paths

def create_simplified_demo_data():
    """
    创建简化版演示数据（100时间步）
    基于你的实际模型：BiGRU、DPTAM-BiGRU、ASB-DPTAM-BiGRU
    """
    print("📊 生成简化版演示数据（100时间步）...")
    
    # 生成100个时间步的风电功率数据
    n_samples = 100
    time_steps = np.arange(n_samples)
    
    # 生成基础风电功率信号
    daily_cycle = 15 * np.sin(2 * np.pi * time_steps / 24)  # 日周期
    trend = 0.05 * time_steps  # 轻微趋势
    random_noise = np.random.normal(0, 2, n_samples)
    
    # 基础功率信号
    base_power = 40 + daily_cycle + trend + random_noise
    y_true = np.maximum(base_power, 1.0)  # 确保功率为正
    
    # 创建你的模型的预测结果
    models_predictions = {}
    
    # 1. BiGRU (基线模型 - 性能最基础)
    noise_bigru = np.random.normal(0, 3.5, n_samples)
    bias_bigru = 0.06 * y_true + np.sin(time_steps * 0.2) * 2.0
    models_predictions['BiGRU'] = y_true + noise_bigru + bias_bigru
    
    # 2. DPTAM-BiGRU (加入时序注意力 - 性能提升)
    noise_dptam = np.random.normal(0, 2.5, n_samples)
    bias_dptam = 0.04 * y_true + np.cos(time_steps * 0.15) * 1.5
    models_predictions['DPTAM-BiGRU'] = y_true + noise_dptam + bias_dptam
    
    # 3. ASB-DPTAM-BiGRU (最完整的模型 - 性能最好)
    noise_asb = np.random.normal(0, 1.5, n_samples)
    bias_asb = 0.02 * y_true + np.sin(time_steps * 0.1) * 0.8
    models_predictions['ASB-DPTAM-BiGRU'] = y_true + noise_asb + bias_asb
    
    # 4. BiLSTM (对比模型)
    noise_bilstm = np.random.normal(0, 3.0, n_samples)
    bias_bilstm = 0.05 * y_true + np.cos(time_steps * 0.18) * 1.8
    models_predictions['BiLSTM'] = y_true + noise_bilstm + bias_bilstm
    
    # 5. CNN-BiGRU (CNN增强的BiGRU)
    noise_cnn_bigru = np.random.normal(0, 2.2, n_samples)
    bias_cnn_bigru = 0.035 * y_true + np.sin(time_steps * 0.12) * 1.2
    models_predictions['CNN-BiGRU'] = y_true + noise_cnn_bigru + bias_cnn_bigru
    
    return y_true, models_predictions

def create_results_format(y_true, models_predictions):
    """转换为UniversalVisualizer格式"""
    results = {}
    
    for model_name, y_pred in models_predictions.items():
        # 计算评估指标
        mae = np.mean(np.abs(y_pred - y_true))
        mse = np.mean((y_pred - y_true) ** 2)
        r2 = 1 - np.sum((y_pred - y_true) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)
        
        results[model_name] = {
            'test': {
                'MAE': mae,
                'MSE': mse,
                'R2': r2
            },
            'predictions': {
                'test': (y_true, y_pred)
            }
        }
    
    return results

def main():
    """主函数"""
    print("🎯 简化版相对百分比误差可视化演示")
    print("=" * 60)
    print("✨ 新特点：时间步长100，无插图，更简洁清晰")
    print()
    
    # 生成演示数据
    y_true, models_predictions = create_simplified_demo_data()
    
    # 转换格式
    results = create_results_format(y_true, models_predictions)
    
    print(f"✅ 生成了 {len(models_predictions)} 个模型的预测数据")
    print(f"📈 数据点数量: {len(y_true)} (缩减至100)")
    
    # 显示模型性能概览
    print("\n📋 你的模型性能概览:")
    print("-" * 50)
    for model_name in models_predictions.keys():
        mae = results[model_name]['test']['MAE']
        r2 = results[model_name]['test']['R2']
        print(f"  {model_name:20s}: MAE={mae:.2f}, R²={r2:.3f}")
    
    # 创建可视化器
    visualizer = UniversalVisualizer()
    
    # 创建保存目录
    save_dir = os.path.join(get_current_paths()['figures'], 'simplified_relative_error')
    os.makedirs(save_dir, exist_ok=True)
    
    # 生成简化版相对百分比误差图
    print("\n🎨 生成简化版相对百分比误差图...")
    
    try:
        filepath = visualizer._plot_relative_percentage_error_simple(
            results=results,
            dataset='test',
            save_dir=save_dir,
            experiment_name='Simplified_Models_Comparison'
        )
        
        if filepath:
            print(f"✅ 简化版图表已保存!")
            print(f"📁 文件路径: {filepath}")
            print(f"📊 文件名: {os.path.basename(filepath)}")
            
            # 显示图表特点
            print("\n🎯 简化版图表特点:")
            print("  ✓ 时间步长：100（从400缩减）")
            print("  ✓ 无插图：更简洁清晰")
            print("  ✓ 展示你的所有模型对比")
            print("  ✓ 不同线型和颜色区分")
            print("  ✓ 论文发表质量 (300 DPI)")
            print("  ✓ 更快的生成速度")
            
        else:
            print("❌ 图表生成失败")
            
    except Exception as e:
        print(f"❌ 生成图表时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n💡 简化版的优势:")
    print("-" * 50)
    print("  • 更快的生成速度")
    print("  • 更清晰的视觉效果")
    print("  • 适合快速模型对比")
    print("  • 减少视觉干扰")
    print("  • 更适合演示和报告")
    
    print("\n🔧 在你的实验中使用:")
    print("-" * 50)
    print("  1. 调用 auto_visualize() 函数")
    print("  2. 简化版相对百分比误差图会自动生成")
    print("  3. 图表保存在 error_analysis 文件夹中")
    print("  4. 文件名不包含 'with_inset'")
    
    print("\n📊 与原版本的对比:")
    print("-" * 50)
    print("  原版本: 400时间步 + 插图 + 复杂布局")
    print("  简化版: 100时间步 + 无插图 + 简洁布局")
    print("  推荐: 简化版更适合日常使用")
    
    print(f"\n🎉 演示完成! 请查看生成的简化版图表")

if __name__ == "__main__":
    main()
