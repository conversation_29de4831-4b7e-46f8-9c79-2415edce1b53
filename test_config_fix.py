#!/usr/bin/env python3
"""
测试配置修复是否成功
"""

import os
import sys

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_import():
    """测试配置导入是否成功"""
    try:
        from src.utils.config import (
            MODEL_CONFIG, LEARNING_RATE_CONFIG, 
            BIGRU_CONFIG, DPTAM_BIGRU_CONFIG, ASB_DPTAM_BIGRU_CONFIG,
            BILSTM_CONFIG, CNN_BIGRU_CONFIG, CNN_BIGRU_ATTENTION_CONFIG
        )
        
        print("✅ 配置导入成功！")
        
        # 测试向后兼容性
        print(f"MODEL_CONFIG['learning_rate']: {MODEL_CONFIG['learning_rate']}")
        print(f"MODEL_CONFIG['base_learning_rate']: {MODEL_CONFIG['base_learning_rate']}")
        
        # 测试差异化学习率
        print("\n📊 差异化学习率配置:")
        for model_name, lr in LEARNING_RATE_CONFIG.items():
            print(f"  {model_name}: {lr}")
        
        # 测试模型配置
        print("\n🏗️ 模型容量配置:")
        models = {
            'ASB-DPTAM-BiGRU': ASB_DPTAM_BIGRU_CONFIG,
            'DPTAM-BiGRU': DPTAM_BIGRU_CONFIG,
            'CNN-BiGRU-Attention': CNN_BIGRU_ATTENTION_CONFIG,
            'CNN-BiGRU': CNN_BIGRU_CONFIG,
            'BiGRU': BIGRU_CONFIG,
            'BiLSTM': BILSTM_CONFIG
        }
        
        for model_name, config in models.items():
            if 'bigru_units' in config:
                units = config['bigru_units']
            elif 'bilstm_units' in config:
                units = config['bilstm_units']
            else:
                units = [0, 0]
            
            dropout = config.get('dropout_rate', 0.3)
            print(f"  {model_name}: 单元数={units}, Dropout={dropout}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_learning_rate_function():
    """测试学习率获取函数"""
    try:
        from src.experiments.asb_dptam_advantage_comparison import ASBDPTAMAdvantageComparator
        
        comparator = ASBDPTAMAdvantageComparator()
        
        print("\n🎯 学习率获取函数测试:")
        test_models = ['BiGRU', 'DPTAM-BiGRU', 'ASB-DPTAM-BiGRU', 'BiLSTM', 'CNN-BiGRU', 'CNN-BiGRU-Attention']
        
        for model_name in test_models:
            lr = comparator.get_learning_rate(model_name)
            print(f"  {model_name}: {lr}")
        
        print("✅ 学习率获取函数测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 学习率获取函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 配置修复测试")
    print("=" * 50)
    
    success1 = test_config_import()
    success2 = test_learning_rate_function()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！配置修复成功")
        print("现在可以运行 ASB-DPTAM 优势对比实验了")
    else:
        print("\n❌ 测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
