#!/usr/bin/env python3
"""
基于你的实际模型的相对百分比误差可视化演示
展示 BiGRU、DPTAM-BiGRU、ASB-DPTAM-BiGRU 等模型的对比
"""

import os
import sys

# 设置环境变量以避免OpenMP冲突
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import numpy as np
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.universal_visualizer import UniversalVisualizer
from src.utils.config import setup_matplotlib, get_current_paths

def create_your_models_demo_data():
    """
    创建基于你的实际模型的演示数据
    模拟 BiGRU、DPTAM-BiGRU、ASB-DPTAM-BiGRU 等模型的预测结果
    """
    print("📊 生成基于你的模型的演示数据...")
    
    # 生成风电功率时间序列数据（缩减至100时间步）
    n_samples = 100
    time_steps = np.arange(n_samples)
    
    # 生成基础风电功率信号
    # 包含日周期、周周期和随机波动
    daily_cycle = 20 * np.sin(2 * np.pi * time_steps / 24)  # 日周期
    weekly_cycle = 10 * np.sin(2 * np.pi * time_steps / 168)  # 周周期
    trend = 0.02 * time_steps  # 轻微趋势
    random_noise = np.random.normal(0, 3, n_samples)
    
    # 基础功率信号
    base_power = 45 + daily_cycle + weekly_cycle + trend + random_noise
    y_true = np.maximum(base_power, 1.0)  # 确保功率为正
    
    # 创建你的模型的预测结果
    models_predictions = {}
    
    # 1. BiGRU (基线模型 - 性能最基础)
    noise_bigru = np.random.normal(0, 4.5, n_samples)
    bias_bigru = 0.08 * y_true + np.sin(time_steps * 0.15) * 2.5
    models_predictions['BiGRU'] = y_true + noise_bigru + bias_bigru
    
    # 2. DPTAM-BiGRU (加入时序注意力 - 性能提升)
    noise_dptam = np.random.normal(0, 3.2, n_samples)
    bias_dptam = 0.05 * y_true + np.cos(time_steps * 0.12) * 1.8
    models_predictions['DPTAM-BiGRU'] = y_true + noise_dptam + bias_dptam
    
    # 3. ASB-DPTAM-BiGRU (最完整的模型 - 性能最好)
    noise_asb = np.random.normal(0, 2.1, n_samples)
    bias_asb = 0.02 * y_true + np.sin(time_steps * 0.08) * 1.0
    models_predictions['ASB-DPTAM-BiGRU'] = y_true + noise_asb + bias_asb
    
    # 4. BiLSTM (对比模型)
    noise_bilstm = np.random.normal(0, 3.8, n_samples)
    bias_bilstm = 0.06 * y_true + np.cos(time_steps * 0.18) * 2.2
    models_predictions['BiLSTM'] = y_true + noise_bilstm + bias_bilstm
    
    # 5. CNN-BiGRU (CNN增强的BiGRU)
    noise_cnn_bigru = np.random.normal(0, 2.8, n_samples)
    bias_cnn_bigru = 0.04 * y_true + np.sin(time_steps * 0.1) * 1.5
    models_predictions['CNN-BiGRU'] = y_true + noise_cnn_bigru + bias_cnn_bigru
    
    # 6. CNN-BiGRU-Attention (CNN+BiGRU+注意力)
    noise_cnn_att = np.random.normal(0, 2.5, n_samples)
    bias_cnn_att = 0.03 * y_true + np.cos(time_steps * 0.09) * 1.2
    models_predictions['CNN-BiGRU-Attention'] = y_true + noise_cnn_att + bias_cnn_att
    
    return y_true, models_predictions

def create_results_format(y_true, models_predictions):
    """转换为UniversalVisualizer格式"""
    results = {}
    
    for model_name, y_pred in models_predictions.items():
        # 计算评估指标
        mae = np.mean(np.abs(y_pred - y_true))
        mse = np.mean((y_pred - y_true) ** 2)
        r2 = 1 - np.sum((y_pred - y_true) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2)
        
        results[model_name] = {
            'test': {
                'MAE': mae,
                'MSE': mse,
                'R2': r2
            },
            'predictions': {
                'test': (y_true, y_pred)
            }
        }
    
    return results

def main():
    """主函数"""
    print("🎯 基于你的模型的相对百分比误差可视化")
    print("=" * 60)
    
    # 生成演示数据
    y_true, models_predictions = create_your_models_demo_data()
    
    # 转换格式
    results = create_results_format(y_true, models_predictions)
    
    print(f"✅ 生成了 {len(models_predictions)} 个模型的预测数据")
    print(f"📈 数据点数量: {len(y_true)}")
    
    # 显示模型性能概览
    print("\n📋 你的模型性能概览:")
    print("-" * 50)
    for model_name in models_predictions.keys():
        mae = results[model_name]['test']['MAE']
        r2 = results[model_name]['test']['R2']
        print(f"  {model_name:20s}: MAE={mae:.2f}, R²={r2:.3f}")
    
    # 创建可视化器
    visualizer = UniversalVisualizer()
    
    # 创建保存目录
    save_dir = os.path.join(get_current_paths()['figures'], 'your_models_relative_error')
    os.makedirs(save_dir, exist_ok=True)
    
    # 生成你的模型的相对百分比误差图
    print("\n🎨 生成你的模型的相对百分比误差图...")
    
    try:
        filepath = visualizer._plot_relative_percentage_error_simple(
            results=results,
            dataset='test',
            save_dir=save_dir,
            experiment_name='Your_Models_Comparison'
        )
        
        if filepath:
            print(f"✅ 你的模型对比图已保存!")
            print(f"📁 文件路径: {filepath}")
            print(f"📊 文件名: {os.path.basename(filepath)}")
            
            # 显示图表特点
            print("\n🎯 简化版图表特点:")
            print("  ✓ 时间步长：100（更简洁）")
            print("  ✓ 无插图：更清晰的视觉效果")
            print("  ✓ BiGRU vs DPTAM-BiGRU vs ASB-DPTAM-BiGRU 对比")
            print("  ✓ 包含CNN增强模型的对比")
            print("  ✓ 论文发表质量 (300 DPI)")
            print("  ✓ 更快的生成速度")
            
        else:
            print("❌ 图表生成失败")
            
    except Exception as e:
        print(f"❌ 生成图表时出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n💡 如何在你的实际实验中使用:")
    print("-" * 50)
    print("  1. 在你的实验脚本中调用 auto_visualize() 函数")
    print("  2. 新的相对百分比误差图会自动生成")
    print("  3. 图表保存在 error_analysis 文件夹中")
    print("  4. 可以直接用于论文或报告")
    
    print("\n🔧 集成示例:")
    print("```python")
    print("# 在你的实验脚本中")
    print("from src.utils.visualization_manager import auto_visualize")
    print("")
    print("models_data = {")
    print("    'BiGRU': (bigru_results, bigru_history),")
    print("    'DPTAM-BiGRU': (dptam_results, dptam_history),")
    print("    'ASB-DPTAM-BiGRU': (asb_results, asb_history)")
    print("}")
    print("")
    print("# 自动生成所有可视化（包括相对百分比误差图）")
    print("files = auto_visualize(models_data, 'ASB_DPTAM_Advantage_Analysis')")
    print("```")
    
    print(f"\n🎉 演示完成! 请查看生成的图表")

if __name__ == "__main__":
    main()
