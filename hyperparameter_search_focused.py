#!/usr/bin/env python3
"""
ASB-DPTAM-BiGRU关键超参数快速搜索
专门优化：n_segment, dptam_kernel_size, bigru_units, dense_units
"""

import itertools
import time
import json
import os
import sys
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel
from src.data_processing.data_loader import DataLoader
from src.data_processing.preprocessor import DataPreprocessor
from src.utils.metrics import ModelEvaluator
from src.utils.config import get_current_paths

class HyperparameterSearcher:
    """ASB-DPTAM-BiGRU超参数搜索器"""
    
    def __init__(self):
        self.search_results = []
        self.best_rmse = float('inf')
        self.best_params = None
        
        # 目标超参数搜索空间
        self.search_space = {
            'n_segment': [4, 6, 8, 12],
            'dptam_kernel_size': [3, 5, 7],
            'bigru_units': [[32,16], [64,32], [128,64], [96,48]],
            'dense_units': [[16,8], [32,16], [64,32], [48,24]]
        }
        
        # 固定的基础配置
        self.base_config = {
            'sequence_length': 24,
            'n_features': 25,
            'dropout_rate': 0.3,
            'asb_adaptive_filter': True,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 50,
            'patience': 15
        }
    
    def load_data(self):
        """加载和预处理数据"""
        print("📊 加载数据...")
        
        data_loader = DataLoader()
        raw_data = data_loader.load_wind_power_data()
        
        preprocessor = DataPreprocessor()
        processed_data = preprocessor.preprocess_for_training(
            raw_data, 
            sequence_length=self.base_config['sequence_length']
        )
        
        return processed_data
    
    def train_single_config(self, params):
        """训练单个配置并返回RMSE"""
        try:
            print(f"🔄 训练配置: {params}")
            
            # 合并参数
            full_config = {**self.base_config, **params}
            
            # 创建模型
            model = ASBDPTAMBiGRUModel(
                sequence_length=full_config['sequence_length'],
                n_features=full_config['n_features'],
                n_segment=full_config['n_segment'],
                dptam_kernel_size=full_config['dptam_kernel_size'],
                bigru_units=full_config['bigru_units'],
                dense_units=full_config['dense_units'],
                dropout_rate=full_config['dropout_rate'],
                asb_adaptive_filter=full_config['asb_adaptive_filter']
            )
            
            # 加载数据
            data = self.load_data()
            
            # 训练模型（简化版，快速验证）
            model.train()
            
            # 这里应该实现实际的训练逻辑
            # 为了演示，我们返回一个模拟的RMSE
            # 实际使用时需要替换为真实的训练和评估代码
            
            # TODO: 实现真实的训练逻辑
            # trainer = ModelTrainer(model, data, full_config)
            # rmse = trainer.train_and_evaluate()
            
            # 模拟RMSE（实际使用时删除这行）
            import random
            rmse = random.uniform(0.03, 0.08)
            
            print(f"   ✅ RMSE: {rmse:.6f}")
            return rmse
            
        except Exception as e:
            print(f"   ❌ 训练失败: {str(e)}")
            return float('inf')
    
    def grid_search(self, search_space=None):
        """网格搜索"""
        if search_space is None:
            search_space = self.search_space
        
        print(f"🔍 开始网格搜索...")
        print(f"📊 搜索空间: {search_space}")
        
        # 生成所有参数组合
        keys = list(search_space.keys())
        values = list(search_space.values())
        combinations = list(itertools.product(*values))
        
        total_combinations = len(combinations)
        print(f"🎯 总组合数: {total_combinations}")
        
        start_time = time.time()
        
        for i, combo in enumerate(combinations):
            params = dict(zip(keys, combo))
            
            print(f"\n📋 试验 {i+1}/{total_combinations}")
            print(f"⏱️  预计剩余时间: {self._estimate_remaining_time(i, total_combinations, start_time)}")
            
            # 训练并评估
            rmse = self.train_single_config(params)
            
            # 记录结果
            result = {
                'trial': i+1,
                'params': params,
                'rmse': rmse,
                'timestamp': datetime.now().isoformat()
            }
            self.search_results.append(result)
            
            # 更新最佳结果
            if rmse < self.best_rmse:
                self.best_rmse = rmse
                self.best_params = params.copy()
                print(f"🎉 新的最佳结果!")
                print(f"   RMSE: {rmse:.6f}")
                print(f"   参数: {params}")
        
        return self.best_params, self.best_rmse
    
    def coarse_to_fine_search(self):
        """粗搜索到细搜索策略"""
        print("🚀 开始粗搜索到细搜索...")
        
        # 第一阶段：粗搜索
        coarse_space = {
            'n_segment': [4, 6, 8],
            'dptam_kernel_size': [3, 5],
            'bigru_units': [[32,16], [64,32]],
            'dense_units': [[16,8], [32,16]]
        }
        
        print("\n🔍 阶段1: 粗搜索")
        coarse_best, coarse_rmse = self.grid_search(coarse_space)
        
        # 第二阶段：在最佳配置附近细搜索
        print(f"\n🔍 阶段2: 基于最佳配置的细搜索")
        print(f"粗搜索最佳: {coarse_best}, RMSE: {coarse_rmse:.6f}")
        
        fine_space = self._expand_around_best(coarse_best)
        fine_best, fine_rmse = self.grid_search(fine_space)
        
        return fine_best, fine_rmse
    
    def _expand_around_best(self, best_params):
        """在最佳参数附近扩展搜索空间"""
        expanded_space = {}
        
        # n_segment扩展
        current_segment = best_params['n_segment']
        segment_options = [4, 6, 8, 12]
        idx = segment_options.index(current_segment)
        expanded_space['n_segment'] = segment_options[max(0,idx-1):min(len(segment_options),idx+2)]
        
        # dptam_kernel_size扩展
        current_kernel = best_params['dptam_kernel_size']
        kernel_options = [3, 5, 7]
        idx = kernel_options.index(current_kernel)
        expanded_space['dptam_kernel_size'] = kernel_options[max(0,idx-1):min(len(kernel_options),idx+2)]
        
        # bigru_units和dense_units保持原有选项
        expanded_space['bigru_units'] = self.search_space['bigru_units']
        expanded_space['dense_units'] = self.search_space['dense_units']
        
        return expanded_space
    
    def _estimate_remaining_time(self, current_trial, total_trials, start_time):
        """估算剩余时间"""
        if current_trial == 0:
            return "计算中..."
        
        elapsed = time.time() - start_time
        avg_time_per_trial = elapsed / current_trial
        remaining_trials = total_trials - current_trial
        remaining_seconds = avg_time_per_trial * remaining_trials
        
        hours = int(remaining_seconds // 3600)
        minutes = int((remaining_seconds % 3600) // 60)
        
        return f"{hours}小时{minutes}分钟"
    
    def save_results(self):
        """保存搜索结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hyperparameter_search_results_{timestamp}.json"
        
        results = {
            'best_params': self.best_params,
            'best_rmse': self.best_rmse,
            'all_results': self.search_results,
            'search_space': self.search_space,
            'base_config': self.base_config
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 结果已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("🚀 ASB-DPTAM-BiGRU超参数搜索开始!")
    print("=" * 60)
    
    searcher = HyperparameterSearcher()
    
    try:
        # 选择搜索策略
        print("请选择搜索策略:")
        print("1. 完整网格搜索 (约6-8小时)")
        print("2. 粗搜索到细搜索 (约3-4小时)")
        
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            best_params, best_rmse = searcher.grid_search()
        elif choice == "2":
            best_params, best_rmse = searcher.coarse_to_fine_search()
        else:
            print("无效选择，使用默认的粗搜索到细搜索策略")
            best_params, best_rmse = searcher.coarse_to_fine_search()
        
        # 显示最终结果
        print("\n" + "=" * 60)
        print("🏆 搜索完成!")
        print(f"🎯 最佳RMSE: {best_rmse:.6f}")
        print(f"🔧 最佳参数:")
        for key, value in best_params.items():
            print(f"   {key}: {value}")
        
        # 保存结果
        searcher.save_results()
        
    except KeyboardInterrupt:
        print("\n⚠️ 搜索被用户中断")
        if searcher.best_params:
            print(f"当前最佳结果: RMSE={searcher.best_rmse:.6f}")
            print(f"当前最佳参数: {searcher.best_params}")
        searcher.save_results()
    
    except Exception as e:
        print(f"\n❌ 搜索过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
