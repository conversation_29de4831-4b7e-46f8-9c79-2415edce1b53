#!/usr/bin/env python3
"""
ASB-DPTAM-BiGRU关键超参数快速搜索
专门优化：n_segment, dptam_kernel_size, bigru_units, dense_units
"""

import itertools
import time
import json
import os
import sys
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.asb_dptam_bigru_model import ASBDPTAMBiGRUModel

try:
    from src.data_processing.data_loader import DataLoader
    from src.data_processing.preprocessor import DataPreprocessor
    from src.utils.metrics import ModelEvaluator
    from src.utils.config import get_current_paths, setup_training_session
    from src.experiments.train_asb_dptam_bigru import ASBDPTAMBiGRUExperiment
    IMPORTS_OK = True
except ImportError as e:
    print(f"⚠️ 导入警告: {e}")
    print("🔄 将使用模拟数据进行测试...")
    IMPORTS_OK = False

class HyperparameterSearcher:
    """ASB-DPTAM-BiGRU超参数搜索器"""
    
    def __init__(self):
        self.search_results = []
        self.best_rmse = float('inf')
        self.best_params = None
        
        # 完整的超参数搜索空间
        self.search_space = {
            'n_segment': [4, 6, 8, 12],
            'dptam_kernel_size': [3, 5, 7],
            'bigru_units': [[32,16], [64,32], [128,64], [96,48]],
            'dense_units': [[16,8], [32,16], [64,32], [48,24]]
        }

        # 完整搜索总组合数: 4 × 3 × 4 × 4 = 192个组合
        
        # 固定的基础配置（针对超参数搜索优化）
        self.base_config = {
            'sequence_length': 24,
            'n_features': 25,
            'dropout_rate': 0.3,
            'asb_adaptive_filter': True,
            'learning_rate': 0.001,
            'batch_size': 32,
            'epochs': 60,              # 增加训练轮数确保充分训练
            'patience': 12             # 适中的早停耐心
        }
    
    def load_data(self):
        """加载和预处理数据"""
        if not hasattr(self, '_cached_data'):
            print("📊 加载数据...")

            if IMPORTS_OK:
                try:
                    # 使用现有的实验框架加载数据
                    experiment = ASBDPTAMBiGRUExperiment()
                    processed_data = experiment.load_and_prepare_data()

                    self._cached_data = processed_data
                    print("✅ 数据加载完成并缓存")

                except Exception as e:
                    print(f"❌ 数据加载失败: {str(e)}")
                    # 创建模拟数据用于测试
                    print("🔄 创建模拟数据进行测试...")
                    self._cached_data = self._create_mock_data()
            else:
                # 如果导入失败，直接使用模拟数据
                print("🔄 使用模拟数据进行超参数搜索测试...")
                self._cached_data = self._create_mock_data()

        return self._cached_data

    def _create_mock_data(self):
        """创建模拟数据用于测试"""
        import torch
        from torch.utils.data import TensorDataset, DataLoader as TorchDataLoader

        # 创建模拟数据
        n_samples = 1000
        seq_len = self.base_config['sequence_length']
        n_features = self.base_config['n_features']

        # 生成模拟的时间序列数据
        X = torch.randn(n_samples, seq_len, n_features)
        y = torch.randn(n_samples, 1)

        # 分割数据
        train_size = int(0.7 * n_samples)
        val_size = int(0.15 * n_samples)

        X_train, y_train = X[:train_size], y[:train_size]
        X_val, y_val = X[train_size:train_size+val_size], y[train_size:train_size+val_size]
        X_test, y_test = X[train_size+val_size:], y[train_size+val_size:]

        # 创建数据加载器
        train_dataset = TensorDataset(X_train, y_train)
        val_dataset = TensorDataset(X_val, y_val)
        test_dataset = TensorDataset(X_test, y_test)

        batch_size = self.base_config['batch_size']

        return {
            'train_loader': TorchDataLoader(train_dataset, batch_size=batch_size, shuffle=True),
            'val_loader': TorchDataLoader(val_dataset, batch_size=batch_size, shuffle=False),
            'test_loader': TorchDataLoader(test_dataset, batch_size=batch_size, shuffle=False)
        }
    
    def train_single_config(self, params):
        """训练单个配置并返回RMSE"""
        try:
            print(f"🔄 训练配置: {params}")

            # 合并参数
            full_config = {**self.base_config, **params}

            # 加载数据（使用缓存）
            data = self.load_data()

            # 创建模型（使用搜索的超参数）
            model = ASBDPTAMBiGRUModel(
                sequence_length=full_config['sequence_length'],
                n_features=full_config['n_features'],
                n_segment=full_config['n_segment'],
                dptam_kernel_size=full_config['dptam_kernel_size'],
                bigru_units=full_config['bigru_units'],
                dense_units=full_config['dense_units'],
                dropout_rate=full_config['dropout_rate'],
                asb_adaptive_filter=full_config['asb_adaptive_filter']
            )

            print(f"   📊 模型参数量: {sum(p.numel() for p in model.parameters()):,}")

            # 训练模型
            rmse = self._train_model(model, data, full_config)

            print(f"   ✅ RMSE: {rmse:.6f}")
            return rmse

        except Exception as e:
            print(f"   ❌ 训练失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return float('inf')

    def _train_model(self, model, data, config):
        """实际的模型训练逻辑"""
        import torch
        import torch.nn as nn
        import torch.optim as optim
        from torch.utils.data import DataLoader as TorchDataLoader

        # 设置设备
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = model.to(device)

        # 设置优化器和损失函数
        optimizer = optim.Adam(model.parameters(),
                             lr=config['learning_rate'],
                             weight_decay=1e-4)
        criterion = nn.MSELoss()

        # 训练循环
        model.train()
        best_val_rmse = float('inf')
        patience_counter = 0

        print(f"   🚀 开始训练 (epochs={config['epochs']}, lr={config['learning_rate']})")

        for epoch in range(config['epochs']):
            # 训练阶段
            train_loss = 0.0
            for batch_idx, (X_batch, y_batch) in enumerate(data['train_loader']):
                X_batch, y_batch = X_batch.to(device), y_batch.to(device)

                optimizer.zero_grad()
                outputs = model(X_batch)
                loss = criterion(outputs, y_batch)
                loss.backward()
                optimizer.step()

                train_loss += loss.item()

            # 验证阶段
            if epoch % 2 == 0:  # 每2轮验证一次
                val_rmse = self._evaluate_model(model, data['val_loader'], device)

                print(f"   📈 Epoch {epoch+1}/{config['epochs']}: "
                      f"Train Loss={train_loss/len(data['train_loader']):.6f}, "
                      f"Val RMSE={val_rmse:.6f}")

                # 早停检查
                if val_rmse < best_val_rmse:
                    best_val_rmse = val_rmse
                    patience_counter = 0
                    # 保存最佳模型状态
                    best_model_state = model.state_dict().copy()
                else:
                    patience_counter += 1

                if patience_counter >= config['patience']:
                    print(f"   ⏹️ 早停触发 (patience={config['patience']})")
                    break

        # 加载最佳模型并测试
        if 'best_model_state' in locals():
            model.load_state_dict(best_model_state)

        test_rmse = self._evaluate_model(model, data['test_loader'], device)
        print(f"   🎯 最终测试 RMSE: {test_rmse:.6f}")

        return test_rmse

    def _evaluate_model(self, model, data_loader, device):
        """评估模型性能"""
        import torch
        import numpy as np

        model.eval()
        predictions = []
        actuals = []

        with torch.no_grad():
            for X_batch, y_batch in data_loader:
                X_batch = X_batch.to(device)
                outputs = model(X_batch)

                predictions.extend(outputs.cpu().numpy().flatten())
                actuals.extend(y_batch.numpy().flatten())

        predictions = np.array(predictions)
        actuals = np.array(actuals)

        # 计算RMSE
        rmse = np.sqrt(np.mean((predictions - actuals) ** 2))

        model.train()  # 恢复训练模式
        return rmse
    
    def grid_search(self, search_space=None):
        """网格搜索"""
        if search_space is None:
            search_space = self.search_space
        
        print(f"🔍 开始网格搜索...")
        print(f"📊 搜索空间: {search_space}")
        
        # 生成所有参数组合
        keys = list(search_space.keys())
        values = list(search_space.values())
        combinations = list(itertools.product(*values))
        
        total_combinations = len(combinations)
        print(f"🎯 总组合数: {total_combinations}")
        
        start_time = time.time()
        
        for i, combo in enumerate(combinations):
            params = dict(zip(keys, combo))
            
            print(f"\n📋 试验 {i+1}/{total_combinations}")
            print(f"⏱️  预计剩余时间: {self._estimate_remaining_time(i, total_combinations, start_time)}")
            
            # 训练并评估
            rmse = self.train_single_config(params)
            
            # 记录结果
            result = {
                'trial': i+1,
                'params': params,
                'rmse': rmse,
                'timestamp': datetime.now().isoformat()
            }
            self.search_results.append(result)
            
            # 更新最佳结果
            if rmse < self.best_rmse:
                self.best_rmse = rmse
                self.best_params = params.copy()
                print(f"🎉 新的最佳结果!")
                print(f"   RMSE: {rmse:.6f}")
                print(f"   参数: {params}")
        
        return self.best_params, self.best_rmse
    
    def coarse_to_fine_search(self):
        """粗搜索到细搜索策略"""
        print("🚀 开始粗搜索到细搜索...")
        
        # 第一阶段：粗搜索 (24个组合)
        coarse_space = {
            'n_segment': [4, 6, 8],                    # 3个选项
            'dptam_kernel_size': [3, 5],               # 2个选项
            'bigru_units': [[32,16], [64,32]],         # 2个选项
            'dense_units': [[16,8], [32,16]]           # 2个选项
        }
        print(f"粗搜索组合数: {3*2*2*2} = 24个")
        
        print("\n🔍 阶段1: 粗搜索")
        coarse_best, coarse_rmse = self.grid_search(coarse_space)
        
        # 第二阶段：在最佳配置附近细搜索
        print(f"\n🔍 阶段2: 基于最佳配置的细搜索")
        print(f"粗搜索最佳: {coarse_best}, RMSE: {coarse_rmse:.6f}")
        
        fine_space = self._expand_around_best(coarse_best)
        fine_best, fine_rmse = self.grid_search(fine_space)
        
        return fine_best, fine_rmse
    
    def _expand_around_best(self, best_params):
        """在最佳参数附近扩展搜索空间"""
        expanded_space = {}

        print(f"🔍 基于最佳配置扩展搜索空间: {best_params}")

        # n_segment扩展
        current_segment = best_params['n_segment']
        segment_options = [4, 6, 8, 12]
        if current_segment in segment_options:
            idx = segment_options.index(current_segment)
            # 包含当前值和相邻值
            start_idx = max(0, idx-1)
            end_idx = min(len(segment_options), idx+2)
            expanded_space['n_segment'] = segment_options[start_idx:end_idx]
            # 如果可能，添加12这个选项
            if 12 not in expanded_space['n_segment']:
                expanded_space['n_segment'].append(12)
        else:
            expanded_space['n_segment'] = [current_segment, 6, 8]

        # dptam_kernel_size扩展
        current_kernel = best_params['dptam_kernel_size']
        kernel_options = [3, 5, 7]
        if current_kernel in kernel_options:
            idx = kernel_options.index(current_kernel)
            start_idx = max(0, idx-1)
            end_idx = min(len(kernel_options), idx+2)
            expanded_space['dptam_kernel_size'] = kernel_options[start_idx:end_idx]
            # 确保包含7这个选项
            if 7 not in expanded_space['dptam_kernel_size']:
                expanded_space['dptam_kernel_size'].append(7)
        else:
            expanded_space['dptam_kernel_size'] = [current_kernel, 3, 5]

        # bigru_units扩展：包含更多选项
        current_bigru = best_params['bigru_units']
        all_bigru_options = [[32,16], [64,32], [128,64], [96,48]]
        expanded_space['bigru_units'] = all_bigru_options

        # dense_units扩展：包含更多选项
        current_dense = best_params['dense_units']
        all_dense_options = [[16,8], [32,16], [64,32], [48,24]]
        expanded_space['dense_units'] = all_dense_options

        # 计算细搜索组合数
        fine_combinations = (len(expanded_space['n_segment']) *
                           len(expanded_space['dptam_kernel_size']) *
                           len(expanded_space['bigru_units']) *
                           len(expanded_space['dense_units']))

        print(f"细搜索空间: {expanded_space}")
        print(f"细搜索组合数: {fine_combinations}")

        return expanded_space
    
    def _estimate_remaining_time(self, current_trial, total_trials, start_time):
        """估算剩余时间"""
        if current_trial == 0:
            return "计算中..."
        
        elapsed = time.time() - start_time
        avg_time_per_trial = elapsed / current_trial
        remaining_trials = total_trials - current_trial
        remaining_seconds = avg_time_per_trial * remaining_trials
        
        hours = int(remaining_seconds // 3600)
        minutes = int((remaining_seconds % 3600) // 60)
        
        return f"{hours}小时{minutes}分钟"
    
    def save_results(self):
        """保存搜索结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hyperparameter_search_results_{timestamp}.json"
        
        results = {
            'best_params': self.best_params,
            'best_rmse': self.best_rmse,
            'all_results': self.search_results,
            'search_space': self.search_space,
            'base_config': self.base_config
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 结果已保存到: {filename}")
        return filename

def main():
    """主函数"""
    print("🚀 ASB-DPTAM-BiGRU超参数搜索开始!")
    print("=" * 60)
    
    searcher = HyperparameterSearcher()
    
    try:
        # 选择搜索策略
        print("请选择搜索策略:")
        print("1. 完整网格搜索 (192个组合, 约12-15小时)")
        print("2. 粗搜索到细搜索 (24+约60个组合, 约6-8小时) [推荐]")
        print("3. 仅粗搜索 (24个组合, 约2-3小时) [快速验证]")

        choice = input("请输入选择 (1, 2, 或 3): ").strip()
        
        if choice == "1":
            print("🚀 开始完整网格搜索...")
            best_params, best_rmse = searcher.grid_search()
        elif choice == "2":
            print("🚀 开始粗搜索到细搜索...")
            best_params, best_rmse = searcher.coarse_to_fine_search()
        elif choice == "3":
            print("🚀 开始仅粗搜索...")
            # 临时修改搜索空间为粗搜索
            original_space = searcher.search_space.copy()
            searcher.search_space = {
                'n_segment': [4, 6, 8],
                'dptam_kernel_size': [3, 5],
                'bigru_units': [[32,16], [64,32]],
                'dense_units': [[16,8], [32,16]]
            }
            best_params, best_rmse = searcher.grid_search()
            searcher.search_space = original_space  # 恢复原始空间
        else:
            print("无效选择，使用默认的粗搜索到细搜索策略")
            best_params, best_rmse = searcher.coarse_to_fine_search()
        
        # 显示最终结果
        print("\n" + "=" * 60)
        print("🏆 搜索完成!")
        print(f"🎯 最佳RMSE: {best_rmse:.6f}")
        print(f"🔧 最佳参数:")
        for key, value in best_params.items():
            print(f"   {key}: {value}")
        
        # 保存结果
        searcher.save_results()
        
    except KeyboardInterrupt:
        print("\n⚠️ 搜索被用户中断")
        if searcher.best_params:
            print(f"当前最佳结果: RMSE={searcher.best_rmse:.6f}")
            print(f"当前最佳参数: {searcher.best_params}")
        searcher.save_results()
    
    except Exception as e:
        print(f"\n❌ 搜索过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
