#!/usr/bin/env python3
"""
在实际实验中使用相对百分比误差可视化的示例
展示如何将新功能集成到现有的实验流程中
"""

import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.visualization_manager import auto_visualize
from src.utils.universal_visualizer import UniversalVisualizer

def example_with_existing_results():
    """
    示例：如何在现有的实验结果中使用相对百分比误差可视化
    """
    print("📊 示例：在现有实验中使用相对百分比误差可视化")
    print("=" * 60)
    
    # 假设你已经有了训练好的模型结果
    # 这里展示数据格式应该是什么样的
    
    # 示例数据格式 - 这应该是你的实际模型结果
    models_data = {
        'BiGRU': (
            {
                # 模型评估指标
                'test': {'MAE': 2.5, 'MSE': 8.2, 'R2': 0.85},
                'train': {'MAE': 2.1, 'MSE': 6.8, 'R2': 0.88},
                'val': {'MAE': 2.3, 'MSE': 7.5, 'R2': 0.86},
                # 预测结果 - (真实值, 预测值)
                'predictions': {
                    'test': (y_true_test, y_pred_bigru_test),
                    'train': (y_true_train, y_pred_bigru_train),
                    'val': (y_true_val, y_pred_bigru_val)
                }
            },
            # 训练历史
            {
                'train_loss': [0.1, 0.08, 0.06, 0.05, 0.04],
                'val_loss': [0.12, 0.09, 0.07, 0.06, 0.055],
                'train_mae': [3.2, 2.8, 2.5, 2.3, 2.1],
                'val_mae': [3.5, 3.0, 2.7, 2.5, 2.3]
            }
        ),
        'DPTAM-BiGRU': (
            {
                'test': {'MAE': 2.1, 'MSE': 6.5, 'R2': 0.89},
                'train': {'MAE': 1.8, 'MSE': 5.2, 'R2': 0.92},
                'val': {'MAE': 2.0, 'MSE': 6.0, 'R2': 0.90},
                'predictions': {
                    'test': (y_true_test, y_pred_dptam_test),
                    'train': (y_true_train, y_pred_dptam_train),
                    'val': (y_true_val, y_pred_dptam_val)
                }
            },
            {
                'train_loss': [0.09, 0.07, 0.05, 0.04, 0.035],
                'val_loss': [0.11, 0.08, 0.06, 0.05, 0.045],
                'train_mae': [3.0, 2.5, 2.2, 2.0, 1.8],
                'val_mae': [3.2, 2.7, 2.4, 2.2, 2.0]
            }
        )
    }
    
    print("💡 使用方法1: 通过可视化管理器自动生成（推荐）")
    print("-" * 40)
    
    # 方法1: 使用可视化管理器 - 会自动包含相对百分比误差图
    try:
        generated_files = auto_visualize(
            models_data=models_data,
            experiment_name="BiGRU_vs_DPTAM_with_RelativeError"
        )
        
        print("✅ 自动生成的可视化文件:")
        for category, files in generated_files.items():
            print(f"  📁 {category}: {len(files)} 个文件")
            for file in files:
                if 'relative_percentage_error' in file:
                    print(f"    🎯 {os.path.basename(file)} (新增的相对百分比误差图)")
                else:
                    print(f"    📊 {os.path.basename(file)}")
                    
    except Exception as e:
        print(f"❌ 自动可视化失败: {str(e)}")
    
    print("\n💡 使用方法2: 直接调用相对百分比误差可视化")
    print("-" * 40)
    
    # 方法2: 直接使用UniversalVisualizer生成特定图表
    try:
        visualizer = UniversalVisualizer()
        
        # 转换数据格式
        results = {}
        for model_name, (model_results, _) in models_data.items():
            results[model_name] = model_results
        
        # 生成相对百分比误差图
        save_dir = "results/figures/custom_relative_error"
        os.makedirs(save_dir, exist_ok=True)
        
        filepath = visualizer._plot_relative_percentage_error_with_inset(
            results=results,
            dataset='test',
            save_dir=save_dir,
            experiment_name='Custom_RelativeError_Analysis'
        )
        
        if filepath:
            print(f"✅ 自定义相对百分比误差图已保存: {filepath}")
        
    except Exception as e:
        print(f"❌ 自定义可视化失败: {str(e)}")

def integration_tips():
    """集成建议和使用技巧"""
    print("\n🔧 集成建议和使用技巧")
    print("=" * 60)
    
    tips = [
        "1. 📈 相对百分比误差图特别适合比较不同模型的预测精度",
        "2. 🎯 插图功能可以突出显示模型性能差异最大的时间段",
        "3. 🎨 图表会自动使用不同的线型和颜色来区分模型",
        "4. 📊 建议在模型对比实验中使用，特别是当有3个以上模型时",
        "5. 🔍 相对百分比误差 = ((预测值 - 真实值) / 真实值) × 100%",
        "6. ⚠️  函数会自动处理除零错误和异常值",
        "7. 📁 图表会自动保存到错误分析(error_analysis)文件夹中",
        "8. 🎛️  可以通过修改display_points参数调整显示的数据点数量"
    ]
    
    for tip in tips:
        print(f"  {tip}")

def customization_options():
    """自定义选项说明"""
    print("\n🎨 自定义选项")
    print("=" * 60)
    
    print("📝 你可以通过修改以下参数来自定义图表:")
    print("  • display_points: 控制显示的数据点数量 (默认: 400)")
    print("  • inset_start/inset_end: 控制插图显示的时间范围")
    print("  • line_styles: 自定义线型样式")
    print("  • colors: 通过_get_high_contrast_color方法自定义颜色")
    print("  • 图表尺寸: 修改figsize参数")
    print("  • 插图位置: 修改inset_axes的位置参数")

if __name__ == "__main__":
    print("🎨 相对百分比误差可视化 - 使用指南")
    print("=" * 60)
    
    print("⚠️  注意: 这是一个使用示例，需要实际的模型数据才能运行")
    print("📖 请参考以下内容了解如何在你的实验中使用这个功能:\n")
    
    # 显示使用示例（不实际运行，因为没有真实数据）
    example_with_existing_results()
    integration_tips()
    customization_options()
    
    print("\n🚀 快速开始:")
    print("  1. 确保你的模型结果包含 'predictions' 字段")
    print("  2. 调用 auto_visualize() 函数，新图表会自动生成")
    print("  3. 查看 error_analysis 文件夹中的相对百分比误差图")
    print("  4. 享受更直观的模型性能对比！")
